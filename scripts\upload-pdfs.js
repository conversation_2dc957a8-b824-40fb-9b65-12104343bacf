#!/usr/bin/env node

/**
 * Firebase Storage PDF Upload <PERSON>ript
 * 
 * This script uploads all PDFs from the assets/pdfs directory to Firebase Storage
 * Run this script to upload your PDFs to the cloud before building your app
 * 
 * Prerequisites:
 * 1. Install Firebase CLI: npm install -g firebase-tools
 * 2. Login to Firebase: firebase login
 * 3. Initialize Firebase in your project: firebase init storage
 * 4. Update the PROJECT_ID below with your Firebase project ID
 */

const fs = require('fs');
const path = require('path');

// Configuration
const PROJECT_ID = 'quiz-bee-techs'; // Replace with your Firebase project ID
const PDFS_DIR = path.join(__dirname, '../assets/pdfs');
const FIREBASE_STORAGE_BUCKET = `${PROJECT_ID}.appspot.com`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getAllPdfFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllPdfFiles(filePath, fileList);
    } else if (path.extname(file).toLowerCase() === '.pdf') {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function getFirebaseStoragePath(localPath) {
  // Convert local path to Firebase Storage path
  const relativePath = path.relative(PDFS_DIR, localPath);
  return `pdfs/${relativePath.replace(/\\/g, '/')}`;
}

function generateUploadCommands() {
  log('\n🔥 Firebase Storage PDF Upload Commands', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  if (!fs.existsSync(PDFS_DIR)) {
    log('❌ PDFs directory not found!', 'red');
    log(`Expected: ${PDFS_DIR}`, 'yellow');
    return;
  }
  
  const pdfFiles = getAllPdfFiles(PDFS_DIR);
  
  if (pdfFiles.length === 0) {
    log('❌ No PDF files found!', 'red');
    return;
  }
  
  log(`\n📚 Found ${pdfFiles.length} PDF files`, 'green');
  log('\n📋 Upload Commands:', 'bright');
  log('Copy and run these commands in your terminal:\n', 'yellow');
  
  // Generate Firebase CLI upload commands
  pdfFiles.forEach((filePath, index) => {
    const storagePath = getFirebaseStoragePath(filePath);
    const fileName = path.basename(filePath);
    const fileSize = (fs.statSync(filePath).size / (1024 * 1024)).toFixed(2);
    
    log(`# ${index + 1}. ${fileName} (${fileSize} MB)`, 'blue');
    log(`firebase storage:upload "${filePath}" "${storagePath}" --project ${PROJECT_ID}`, 'green');
    log('');
  });
  
  log('\n🔧 Setup Commands (run once):', 'bright');
  log('npm install -g firebase-tools', 'cyan');
  log('firebase login', 'cyan');
  log(`firebase use ${PROJECT_ID}`, 'cyan');
  log('firebase init storage', 'cyan');
  
  log('\n📝 After uploading, update your Firebase project ID in:', 'bright');
  log('- src/config/pdfConfig.js', 'yellow');
  log(`- Replace "your-project-id" with "${PROJECT_ID}"`, 'yellow');
  
  log('\n🔐 Make sure your Firebase Storage rules allow public read access:', 'bright');
  log('rules_version = \'2\';', 'cyan');
  log('service firebase.storage {', 'cyan');
  log('  match /b/{bucket}/o {', 'cyan');
  log('    match /pdfs/{allPaths=**} {', 'cyan');
  log('      allow read: if true;', 'cyan');
  log('    }', 'cyan');
  log('  }', 'cyan');
  log('}', 'cyan');
}

function generateBatchUploadScript() {
  const pdfFiles = getAllPdfFiles(PDFS_DIR);
  
  if (pdfFiles.length === 0) return;
  
  log('\n📦 Batch Upload Script (Windows):', 'bright');
  log('Save this as upload-all-pdfs.bat:\n', 'yellow');
  
  let batchScript = '@echo off\n';
  batchScript += 'echo Uploading PDFs to Firebase Storage...\n';
  batchScript += `firebase use ${PROJECT_ID}\n`;
  
  pdfFiles.forEach(filePath => {
    const storagePath = getFirebaseStoragePath(filePath);
    batchScript += `firebase storage:upload "${filePath}" "${storagePath}" --project ${PROJECT_ID}\n`;
  });
  
  batchScript += 'echo Upload complete!\npause\n';
  
  fs.writeFileSync('upload-all-pdfs.bat', batchScript);
  log('✅ Created upload-all-pdfs.bat', 'green');
  
  // PowerShell version
  log('\n📦 Batch Upload Script (PowerShell):', 'bright');
  log('Save this as upload-all-pdfs.ps1:\n', 'yellow');
  
  let psScript = 'Write-Host "Uploading PDFs to Firebase Storage..."\n';
  psScript += `firebase use ${PROJECT_ID}\n`;
  
  pdfFiles.forEach(filePath => {
    const storagePath = getFirebaseStoragePath(filePath);
    psScript += `firebase storage:upload "${filePath}" "${storagePath}" --project ${PROJECT_ID}\n`;
  });
  
  psScript += 'Write-Host "Upload complete!"\n';
  
  fs.writeFileSync('upload-all-pdfs.ps1', psScript);
  log('✅ Created upload-all-pdfs.ps1', 'green');
}

// Main execution
if (require.main === module) {
  log('🚀 Firebase Storage PDF Upload Helper', 'bright');
  
  if (PROJECT_ID === 'your-firebase-project-id') {
    log('\n⚠️  Please update PROJECT_ID in this script first!', 'red');
    log('Edit scripts/upload-pdfs.js and replace "your-firebase-project-id"', 'yellow');
    process.exit(1);
  }
  
  generateUploadCommands();
  generateBatchUploadScript();
  
  log('\n🎉 Ready to upload! Follow the commands above.', 'green');
}
