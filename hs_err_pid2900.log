#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1136336 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=2900, tid=21248
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -XX:MaxMetaspaceSize=384m -XX:+HeapDumpOnOutOfMemoryError -Xms256m -Xmx512m -Dfile.encoding=utf8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9

Host: AMD Ryzen 7 4800H with Radeon Graphics         , 16 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Mon May 26 17:19:08 2025 India Standard Time elapsed time: 158.036983 seconds (0d 0h 2m 38s)

---------------  T H R E A D  ---------------

Current thread (0x0000019467039f30):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=21248, stack(0x0000000adb900000,0x0000000adba00000) (1024K)]


Current CompileTask:
C2:158037 4813       4       jdk.internal.jimage.ImageLocation::getFullName (109 bytes)

Stack: [0x0000000adb900000,0x0000000adba00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001946dbef8b0, length=38, elements={
0x00000194364bf010, 0x000001946702b4a0, 0x000001946702cc10, 0x0000019467031790,
0x00000194670335a0, 0x0000019467034d10, 0x0000019467038490, 0x0000019467039f30,
0x000001946706aca0, 0x0000019467153ea0, 0x0000019467156600, 0x0000019467156c90,
0x0000019467153810, 0x0000019467155f70, 0x0000019467154bc0, 0x0000019467155250,
0x00000194671558e0, 0x000001946ce70cf0, 0x000001946ce6ffd0, 0x000001946ce71380,
0x000001946ce72730, 0x000001946ce72dc0, 0x000001946ce73450, 0x000001946ce720a0,
0x000001946ce71a10, 0x000001946ce70660, 0x000001946ce76f60, 0x000001946ce75520,
0x000001946ce775f0, 0x000001946ce74800, 0x000001946ce74170, 0x000001946ce76240,
0x000001946ce74e90, 0x000001946ce75bb0, 0x000001946ce768d0, 0x000001946e7d5c30,
0x000001946e7d41f0, 0x000001946e7d62c0
}

Java Threads: ( => current thread )
  0x00000194364bf010 JavaThread "main"                              [_thread_blocked, id=25568, stack(0x0000000adab00000,0x0000000adac00000) (1024K)]
  0x000001946702b4a0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=24744, stack(0x0000000adb300000,0x0000000adb400000) (1024K)]
  0x000001946702cc10 JavaThread "Finalizer"                  daemon [_thread_blocked, id=32464, stack(0x0000000adb400000,0x0000000adb500000) (1024K)]
  0x0000019467031790 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=24968, stack(0x0000000adb500000,0x0000000adb600000) (1024K)]
  0x00000194670335a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=5648, stack(0x0000000adb600000,0x0000000adb700000) (1024K)]
  0x0000019467034d10 JavaThread "Service Thread"             daemon [_thread_blocked, id=30656, stack(0x0000000adb700000,0x0000000adb800000) (1024K)]
  0x0000019467038490 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=20824, stack(0x0000000adb800000,0x0000000adb900000) (1024K)]
=>0x0000019467039f30 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=21248, stack(0x0000000adb900000,0x0000000adba00000) (1024K)]
  0x000001946706aca0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=18152, stack(0x0000000adba00000,0x0000000adbb00000) (1024K)]
  0x0000019467153ea0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=31856, stack(0x0000000adbb00000,0x0000000adbc00000) (1024K)]
  0x0000019467156600 JavaThread "Notification Thread"        daemon [_thread_blocked, id=12424, stack(0x0000000adbd00000,0x0000000adbe00000) (1024K)]
  0x0000019467156c90 JavaThread "Daemon health stats"               [_thread_blocked, id=29952, stack(0x0000000adc300000,0x0000000adc400000) (1024K)]
  0x0000019467153810 JavaThread "Incoming local TCP Connector on port 51920"        [_thread_in_native, id=21204, stack(0x0000000adc400000,0x0000000adc500000) (1024K)]
  0x0000019467155f70 JavaThread "Daemon periodic checks"            [_thread_blocked, id=27664, stack(0x0000000adc700000,0x0000000adc800000) (1024K)]
  0x0000019467154bc0 JavaThread "Daemon"                            [_thread_blocked, id=24764, stack(0x0000000adc600000,0x0000000adc700000) (1024K)]
  0x0000019467155250 JavaThread "Handler for socket connection from /127.0.0.1:51920 to /127.0.0.1:51923"        [_thread_in_native, id=30472, stack(0x0000000adc800000,0x0000000adc900000) (1024K)]
  0x00000194671558e0 JavaThread "Cancel handler"                    [_thread_blocked, id=11232, stack(0x0000000adc900000,0x0000000adca00000) (1024K)]
  0x000001946ce70cf0 JavaThread "Daemon worker"                     [_thread_in_vm, id=26636, stack(0x0000000adca00000,0x0000000adcb00000) (1024K)]
  0x000001946ce6ffd0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51920 to /127.0.0.1:51923"        [_thread_blocked, id=30496, stack(0x0000000adcb00000,0x0000000adcc00000) (1024K)]
  0x000001946ce71380 JavaThread "Stdin handler"                     [_thread_blocked, id=22804, stack(0x0000000adcc00000,0x0000000adcd00000) (1024K)]
  0x000001946ce72730 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=31512, stack(0x0000000adcd00000,0x0000000adce00000) (1024K)]
  0x000001946ce72dc0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=21844, stack(0x0000000adce00000,0x0000000adcf00000) (1024K)]
  0x000001946ce73450 JavaThread "File lock request listener"        [_thread_in_native, id=28372, stack(0x0000000adcf00000,0x0000000add000000) (1024K)]
  0x000001946ce720a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.9\fileHashes)"        [_thread_blocked, id=19868, stack(0x0000000add000000,0x0000000add100000) (1024K)]
  0x000001946ce71a10 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\.gradle\8.9\fileHashes)"        [_thread_blocked, id=31276, stack(0x0000000add800000,0x0000000add900000) (1024K)]
  0x000001946ce70660 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=16120, stack(0x0000000add900000,0x0000000adda00000) (1024K)]
  0x000001946ce76f60 JavaThread "File lock release action executor"        [_thread_blocked, id=33436, stack(0x0000000adda00000,0x0000000addb00000) (1024K)]
  0x000001946ce75520 JavaThread "File watcher server"        daemon [_thread_in_native, id=29168, stack(0x0000000addb00000,0x0000000addc00000) (1024K)]
  0x000001946ce775f0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=13780, stack(0x0000000addc00000,0x0000000addd00000) (1024K)]
  0x000001946ce74800 JavaThread "jar transforms"                    [_thread_blocked, id=14260, stack(0x0000000addd00000,0x0000000adde00000) (1024K)]
  0x000001946ce74170 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=24908, stack(0x0000000adde00000,0x0000000addf00000) (1024K)]
  0x000001946ce76240 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=30428, stack(0x0000000addf00000,0x0000000ade000000) (1024K)]
  0x000001946ce74e90 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\quiz-bee-techs\node_modules\expo\android\.gradle\8.9\checksums)"        [_thread_blocked, id=3664, stack(0x0000000ada800000,0x0000000ada900000) (1024K)]
  0x000001946ce75bb0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.9\fileContent)"        [_thread_blocked, id=3680, stack(0x0000000ade100000,0x0000000ade200000) (1024K)]
  0x000001946ce768d0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.9\md-rule)"        [_thread_blocked, id=7064, stack(0x0000000ade200000,0x0000000ade300000) (1024K)]
  0x000001946e7d5c30 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.9\md-supplier)"        [_thread_blocked, id=30916, stack(0x0000000ade000000,0x0000000ade100000) (1024K)]
  0x000001946e7d41f0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=31772, stack(0x0000000ade300000,0x0000000ade400000) (1024K)]
  0x000001946e7d62c0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=31224, stack(0x0000000ade400000,0x0000000ade500000) (1024K)]
Total: 38

Other Threads:
  0x000001946700a190 VMThread "VM Thread"                           [id=13580, stack(0x0000000adb200000,0x0000000adb300000) (1024K)]
  0x0000019451dea510 WatcherThread "VM Periodic Task Thread"        [id=31804, stack(0x0000000adb100000,0x0000000adb200000) (1024K)]
  0x00000194364e9d30 WorkerThread "GC Thread#0"                     [id=18372, stack(0x0000000adac00000,0x0000000adad00000) (1024K)]
  0x00000194674755f0 WorkerThread "GC Thread#1"                     [id=8920, stack(0x0000000adbc00000,0x0000000adbd00000) (1024K)]
  0x00000194678e5f40 WorkerThread "GC Thread#2"                     [id=21288, stack(0x0000000adbe00000,0x0000000adbf00000) (1024K)]
  0x0000019467824a80 WorkerThread "GC Thread#3"                     [id=8500, stack(0x0000000adbf00000,0x0000000adc000000) (1024K)]
  0x0000019467824e20 WorkerThread "GC Thread#4"                     [id=10232, stack(0x0000000adc000000,0x0000000adc100000) (1024K)]
  0x000001946707cd40 WorkerThread "GC Thread#5"                     [id=6928, stack(0x0000000adc100000,0x0000000adc200000) (1024K)]
  0x000001946d752270 WorkerThread "GC Thread#6"                     [id=31912, stack(0x0000000ada900000,0x0000000adaa00000) (1024K)]
  0x000001946d6be610 WorkerThread "GC Thread#7"                     [id=30084, stack(0x0000000adaa00000,0x0000000adab00000) (1024K)]
  0x000001946d44a220 WorkerThread "GC Thread#8"                     [id=25236, stack(0x0000000add100000,0x0000000add200000) (1024K)]
  0x000001946d44a5c0 WorkerThread "GC Thread#9"                     [id=25704, stack(0x0000000add200000,0x0000000add300000) (1024K)]
  0x000001946d44a960 WorkerThread "GC Thread#10"                    [id=30092, stack(0x0000000add300000,0x0000000add400000) (1024K)]
  0x000001946d449e80 WorkerThread "GC Thread#11"                    [id=29968, stack(0x0000000add400000,0x0000000add500000) (1024K)]
  0x000001946d449ae0 WorkerThread "GC Thread#12"                    [id=30360, stack(0x0000000add500000,0x0000000add600000) (1024K)]
  0x00000194364efad0 ConcurrentGCThread "G1 Main Marker"            [id=5344, stack(0x0000000adad00000,0x0000000adae00000) (1024K)]
  0x00000194364f0380 WorkerThread "G1 Conc#0"                       [id=26552, stack(0x0000000adae00000,0x0000000adaf00000) (1024K)]
  0x000001946d449740 WorkerThread "G1 Conc#1"                       [id=12648, stack(0x0000000add600000,0x0000000add700000) (1024K)]
  0x000001946d44ad00 WorkerThread "G1 Conc#2"                       [id=20612, stack(0x0000000add700000,0x0000000add800000) (1024K)]
  0x0000019451cbae80 ConcurrentGCThread "G1 Refine#0"               [id=30484, stack(0x0000000adaf00000,0x0000000adb000000) (1024K)]
  0x0000019451cbc3c0 ConcurrentGCThread "G1 Service"                [id=29000, stack(0x0000000adb000000,0x0000000adb100000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread0  158180 4813       4       jdk.internal.jimage.ImageLocation::getFullName (109 bytes)
C1 CompilerThread0  158180 4975       3       org.gradle.internal.service.DefaultServiceRegistry::isSatisfiedBy (61 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd8105ce88] MethodCompileQueue_lock - owner thread: 0x000001946706aca0

Heap address: 0x00000000e0000000, size: 512 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019452000000-0x0000019452c80000-0x0000019452c80000), size 13107200, SharedBaseAddress: 0x0000019452000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019453000000-0x0000019467000000, reserved size: 335544320
Narrow klass base: 0x0000019452000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 16 total, 16 available
 Memory: 7599M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 256M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 512M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 262144K, used 79411K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 51 young (52224K), 9 survivors (9216K)
 Metaspace       used 40061K, committed 41344K, reserved 393216K
  class space    used 5563K, committed 6208K, reserved 327680K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000e0000000, 0x00000000e0100000, 0x00000000e0100000|100%|HS|  |TAMS 0x00000000e0000000| PB 0x00000000e0000000| Complete 
|   1|0x00000000e0100000, 0x00000000e0200000, 0x00000000e0200000|100%|HC|  |TAMS 0x00000000e0100000| PB 0x00000000e0100000| Complete 
|   2|0x00000000e0200000, 0x00000000e0300000, 0x00000000e0300000|100%|HC|  |TAMS 0x00000000e0200000| PB 0x00000000e0200000| Complete 
|   3|0x00000000e0300000, 0x00000000e0400000, 0x00000000e0400000|100%|HC|  |TAMS 0x00000000e0300000| PB 0x00000000e0300000| Complete 
|   4|0x00000000e0400000, 0x00000000e0500000, 0x00000000e0500000|100%|HS|  |TAMS 0x00000000e0400000| PB 0x00000000e0400000| Complete 
|   5|0x00000000e0500000, 0x00000000e0600000, 0x00000000e0600000|100%| O|  |TAMS 0x00000000e0500000| PB 0x00000000e0500000| Untracked 
|   6|0x00000000e0600000, 0x00000000e0700000, 0x00000000e0700000|100%| O|  |TAMS 0x00000000e0600000| PB 0x00000000e0600000| Untracked 
|   7|0x00000000e0700000, 0x00000000e0800000, 0x00000000e0800000|100%| O|  |TAMS 0x00000000e0700000| PB 0x00000000e0700000| Untracked 
|   8|0x00000000e0800000, 0x00000000e0900000, 0x00000000e0900000|100%| O|  |TAMS 0x00000000e0800000| PB 0x00000000e0800000| Untracked 
|   9|0x00000000e0900000, 0x00000000e0a00000, 0x00000000e0a00000|100%| O|  |TAMS 0x00000000e0900000| PB 0x00000000e0900000| Untracked 
|  10|0x00000000e0a00000, 0x00000000e0b00000, 0x00000000e0b00000|100%|HS|  |TAMS 0x00000000e0a00000| PB 0x00000000e0a00000| Complete 
|  11|0x00000000e0b00000, 0x00000000e0c00000, 0x00000000e0c00000|100%| O|  |TAMS 0x00000000e0b00000| PB 0x00000000e0b00000| Untracked 
|  12|0x00000000e0c00000, 0x00000000e0d00000, 0x00000000e0d00000|100%| O|  |TAMS 0x00000000e0c00000| PB 0x00000000e0c00000| Untracked 
|  13|0x00000000e0d00000, 0x00000000e0e00000, 0x00000000e0e00000|100%| O|  |TAMS 0x00000000e0d00000| PB 0x00000000e0d00000| Untracked 
|  14|0x00000000e0e00000, 0x00000000e0f00000, 0x00000000e0f00000|100%| O|  |TAMS 0x00000000e0e00000| PB 0x00000000e0e00000| Untracked 
|  15|0x00000000e0f00000, 0x00000000e1000000, 0x00000000e1000000|100%| O|  |TAMS 0x00000000e0f00000| PB 0x00000000e0f00000| Untracked 
|  16|0x00000000e1000000, 0x00000000e1100000, 0x00000000e1100000|100%| O|Cm|TAMS 0x00000000e1000000| PB 0x00000000e1000000| Complete 
|  17|0x00000000e1100000, 0x00000000e1200000, 0x00000000e1200000|100%| O|  |TAMS 0x00000000e1100000| PB 0x00000000e1100000| Untracked 
|  18|0x00000000e1200000, 0x00000000e1300000, 0x00000000e1300000|100%| O|  |TAMS 0x00000000e1200000| PB 0x00000000e1200000| Untracked 
|  19|0x00000000e1300000, 0x00000000e1400000, 0x00000000e1400000|100%| O|  |TAMS 0x00000000e1300000| PB 0x00000000e1300000| Untracked 
|  20|0x00000000e1400000, 0x00000000e1500000, 0x00000000e1500000|100%| O|  |TAMS 0x00000000e1400000| PB 0x00000000e1400000| Untracked 
|  21|0x00000000e1500000, 0x00000000e1600000, 0x00000000e1600000|100%| O|  |TAMS 0x00000000e1500000| PB 0x00000000e1500000| Untracked 
|  22|0x00000000e1600000, 0x00000000e1700000, 0x00000000e1700000|100%| O|  |TAMS 0x00000000e1600000| PB 0x00000000e1600000| Untracked 
|  23|0x00000000e1700000, 0x00000000e1800000, 0x00000000e1800000|100%| O|  |TAMS 0x00000000e1700000| PB 0x00000000e1700000| Untracked 
|  24|0x00000000e1800000, 0x00000000e188cd90, 0x00000000e1900000| 55%| O|  |TAMS 0x00000000e1800000| PB 0x00000000e1800000| Untracked 
|  25|0x00000000e1900000, 0x00000000e1a00000, 0x00000000e1a00000|100%|HS|  |TAMS 0x00000000e1900000| PB 0x00000000e1900000| Complete 
|  26|0x00000000e1a00000, 0x00000000e1b00000, 0x00000000e1b00000|100%|HC|  |TAMS 0x00000000e1a00000| PB 0x00000000e1a00000| Complete 
|  27|0x00000000e1b00000, 0x00000000e1c00000, 0x00000000e1c00000|100%|HS|  |TAMS 0x00000000e1b00000| PB 0x00000000e1b00000| Complete 
|  28|0x00000000e1c00000, 0x00000000e1d00000, 0x00000000e1d00000|100%|HC|  |TAMS 0x00000000e1c00000| PB 0x00000000e1c00000| Complete 
|  29|0x00000000e1d00000, 0x00000000e1d00000, 0x00000000e1e00000|  0%| F|  |TAMS 0x00000000e1d00000| PB 0x00000000e1d00000| Untracked 
|  30|0x00000000e1e00000, 0x00000000e1e00000, 0x00000000e1f00000|  0%| F|  |TAMS 0x00000000e1e00000| PB 0x00000000e1e00000| Untracked 
|  31|0x00000000e1f00000, 0x00000000e1f00000, 0x00000000e2000000|  0%| F|  |TAMS 0x00000000e1f00000| PB 0x00000000e1f00000| Untracked 
|  32|0x00000000e2000000, 0x00000000e2000000, 0x00000000e2100000|  0%| F|  |TAMS 0x00000000e2000000| PB 0x00000000e2000000| Untracked 
|  33|0x00000000e2100000, 0x00000000e2100000, 0x00000000e2200000|  0%| F|  |TAMS 0x00000000e2100000| PB 0x00000000e2100000| Untracked 
|  34|0x00000000e2200000, 0x00000000e2200000, 0x00000000e2300000|  0%| F|  |TAMS 0x00000000e2200000| PB 0x00000000e2200000| Untracked 
|  35|0x00000000e2300000, 0x00000000e2300000, 0x00000000e2400000|  0%| F|  |TAMS 0x00000000e2300000| PB 0x00000000e2300000| Untracked 
|  36|0x00000000e2400000, 0x00000000e2400000, 0x00000000e2500000|  0%| F|  |TAMS 0x00000000e2400000| PB 0x00000000e2400000| Untracked 
|  37|0x00000000e2500000, 0x00000000e2500000, 0x00000000e2600000|  0%| F|  |TAMS 0x00000000e2500000| PB 0x00000000e2500000| Untracked 
|  38|0x00000000e2600000, 0x00000000e2600000, 0x00000000e2700000|  0%| F|  |TAMS 0x00000000e2600000| PB 0x00000000e2600000| Untracked 
|  39|0x00000000e2700000, 0x00000000e2700000, 0x00000000e2800000|  0%| F|  |TAMS 0x00000000e2700000| PB 0x00000000e2700000| Untracked 
|  40|0x00000000e2800000, 0x00000000e2800000, 0x00000000e2900000|  0%| F|  |TAMS 0x00000000e2800000| PB 0x00000000e2800000| Untracked 
|  41|0x00000000e2900000, 0x00000000e2900000, 0x00000000e2a00000|  0%| F|  |TAMS 0x00000000e2900000| PB 0x00000000e2900000| Untracked 
|  42|0x00000000e2a00000, 0x00000000e2a00000, 0x00000000e2b00000|  0%| F|  |TAMS 0x00000000e2a00000| PB 0x00000000e2a00000| Untracked 
|  43|0x00000000e2b00000, 0x00000000e2b00000, 0x00000000e2c00000|  0%| F|  |TAMS 0x00000000e2b00000| PB 0x00000000e2b00000| Untracked 
|  44|0x00000000e2c00000, 0x00000000e2c00000, 0x00000000e2d00000|  0%| F|  |TAMS 0x00000000e2c00000| PB 0x00000000e2c00000| Untracked 
|  45|0x00000000e2d00000, 0x00000000e2d00000, 0x00000000e2e00000|  0%| F|  |TAMS 0x00000000e2d00000| PB 0x00000000e2d00000| Untracked 
|  46|0x00000000e2e00000, 0x00000000e2e00000, 0x00000000e2f00000|  0%| F|  |TAMS 0x00000000e2e00000| PB 0x00000000e2e00000| Untracked 
|  47|0x00000000e2f00000, 0x00000000e2f00000, 0x00000000e3000000|  0%| F|  |TAMS 0x00000000e2f00000| PB 0x00000000e2f00000| Untracked 
|  48|0x00000000e3000000, 0x00000000e3000000, 0x00000000e3100000|  0%| F|  |TAMS 0x00000000e3000000| PB 0x00000000e3000000| Untracked 
|  49|0x00000000e3100000, 0x00000000e3100000, 0x00000000e3200000|  0%| F|  |TAMS 0x00000000e3100000| PB 0x00000000e3100000| Untracked 
|  50|0x00000000e3200000, 0x00000000e3200000, 0x00000000e3300000|  0%| F|  |TAMS 0x00000000e3200000| PB 0x00000000e3200000| Untracked 
|  51|0x00000000e3300000, 0x00000000e3300000, 0x00000000e3400000|  0%| F|  |TAMS 0x00000000e3300000| PB 0x00000000e3300000| Untracked 
|  52|0x00000000e3400000, 0x00000000e3400000, 0x00000000e3500000|  0%| F|  |TAMS 0x00000000e3400000| PB 0x00000000e3400000| Untracked 
|  53|0x00000000e3500000, 0x00000000e3500000, 0x00000000e3600000|  0%| F|  |TAMS 0x00000000e3500000| PB 0x00000000e3500000| Untracked 
|  54|0x00000000e3600000, 0x00000000e3600000, 0x00000000e3700000|  0%| F|  |TAMS 0x00000000e3600000| PB 0x00000000e3600000| Untracked 
|  55|0x00000000e3700000, 0x00000000e3700000, 0x00000000e3800000|  0%| F|  |TAMS 0x00000000e3700000| PB 0x00000000e3700000| Untracked 
|  56|0x00000000e3800000, 0x00000000e3800000, 0x00000000e3900000|  0%| F|  |TAMS 0x00000000e3800000| PB 0x00000000e3800000| Untracked 
|  57|0x00000000e3900000, 0x00000000e3900000, 0x00000000e3a00000|  0%| F|  |TAMS 0x00000000e3900000| PB 0x00000000e3900000| Untracked 
|  58|0x00000000e3a00000, 0x00000000e3a00000, 0x00000000e3b00000|  0%| F|  |TAMS 0x00000000e3a00000| PB 0x00000000e3a00000| Untracked 
|  59|0x00000000e3b00000, 0x00000000e3b00000, 0x00000000e3c00000|  0%| F|  |TAMS 0x00000000e3b00000| PB 0x00000000e3b00000| Untracked 
|  60|0x00000000e3c00000, 0x00000000e3c00000, 0x00000000e3d00000|  0%| F|  |TAMS 0x00000000e3c00000| PB 0x00000000e3c00000| Untracked 
|  61|0x00000000e3d00000, 0x00000000e3d00000, 0x00000000e3e00000|  0%| F|  |TAMS 0x00000000e3d00000| PB 0x00000000e3d00000| Untracked 
|  62|0x00000000e3e00000, 0x00000000e3e00000, 0x00000000e3f00000|  0%| F|  |TAMS 0x00000000e3e00000| PB 0x00000000e3e00000| Untracked 
|  63|0x00000000e3f00000, 0x00000000e3f00000, 0x00000000e4000000|  0%| F|  |TAMS 0x00000000e3f00000| PB 0x00000000e3f00000| Untracked 
|  64|0x00000000e4000000, 0x00000000e4000000, 0x00000000e4100000|  0%| F|  |TAMS 0x00000000e4000000| PB 0x00000000e4000000| Untracked 
|  65|0x00000000e4100000, 0x00000000e4100000, 0x00000000e4200000|  0%| F|  |TAMS 0x00000000e4100000| PB 0x00000000e4100000| Untracked 
|  66|0x00000000e4200000, 0x00000000e4200000, 0x00000000e4300000|  0%| F|  |TAMS 0x00000000e4200000| PB 0x00000000e4200000| Untracked 
|  67|0x00000000e4300000, 0x00000000e4300000, 0x00000000e4400000|  0%| F|  |TAMS 0x00000000e4300000| PB 0x00000000e4300000| Untracked 
|  68|0x00000000e4400000, 0x00000000e4400000, 0x00000000e4500000|  0%| F|  |TAMS 0x00000000e4400000| PB 0x00000000e4400000| Untracked 
|  69|0x00000000e4500000, 0x00000000e4500000, 0x00000000e4600000|  0%| F|  |TAMS 0x00000000e4500000| PB 0x00000000e4500000| Untracked 
|  70|0x00000000e4600000, 0x00000000e4600000, 0x00000000e4700000|  0%| F|  |TAMS 0x00000000e4600000| PB 0x00000000e4600000| Untracked 
|  71|0x00000000e4700000, 0x00000000e4700000, 0x00000000e4800000|  0%| F|  |TAMS 0x00000000e4700000| PB 0x00000000e4700000| Untracked 
|  72|0x00000000e4800000, 0x00000000e4800000, 0x00000000e4900000|  0%| F|  |TAMS 0x00000000e4800000| PB 0x00000000e4800000| Untracked 
|  73|0x00000000e4900000, 0x00000000e4900000, 0x00000000e4a00000|  0%| F|  |TAMS 0x00000000e4900000| PB 0x00000000e4900000| Untracked 
|  74|0x00000000e4a00000, 0x00000000e4a00000, 0x00000000e4b00000|  0%| F|  |TAMS 0x00000000e4a00000| PB 0x00000000e4a00000| Untracked 
|  75|0x00000000e4b00000, 0x00000000e4b00000, 0x00000000e4c00000|  0%| F|  |TAMS 0x00000000e4b00000| PB 0x00000000e4b00000| Untracked 
|  76|0x00000000e4c00000, 0x00000000e4c00000, 0x00000000e4d00000|  0%| F|  |TAMS 0x00000000e4c00000| PB 0x00000000e4c00000| Untracked 
|  77|0x00000000e4d00000, 0x00000000e4d00000, 0x00000000e4e00000|  0%| F|  |TAMS 0x00000000e4d00000| PB 0x00000000e4d00000| Untracked 
|  78|0x00000000e4e00000, 0x00000000e4e00000, 0x00000000e4f00000|  0%| F|  |TAMS 0x00000000e4e00000| PB 0x00000000e4e00000| Untracked 
|  79|0x00000000e4f00000, 0x00000000e4f00000, 0x00000000e5000000|  0%| F|  |TAMS 0x00000000e4f00000| PB 0x00000000e4f00000| Untracked 
|  80|0x00000000e5000000, 0x00000000e5000000, 0x00000000e5100000|  0%| F|  |TAMS 0x00000000e5000000| PB 0x00000000e5000000| Untracked 
|  81|0x00000000e5100000, 0x00000000e5100000, 0x00000000e5200000|  0%| F|  |TAMS 0x00000000e5100000| PB 0x00000000e5100000| Untracked 
|  82|0x00000000e5200000, 0x00000000e5200000, 0x00000000e5300000|  0%| F|  |TAMS 0x00000000e5200000| PB 0x00000000e5200000| Untracked 
|  83|0x00000000e5300000, 0x00000000e5300000, 0x00000000e5400000|  0%| F|  |TAMS 0x00000000e5300000| PB 0x00000000e5300000| Untracked 
|  84|0x00000000e5400000, 0x00000000e5400000, 0x00000000e5500000|  0%| F|  |TAMS 0x00000000e5400000| PB 0x00000000e5400000| Untracked 
|  85|0x00000000e5500000, 0x00000000e5500000, 0x00000000e5600000|  0%| F|  |TAMS 0x00000000e5500000| PB 0x00000000e5500000| Untracked 
|  86|0x00000000e5600000, 0x00000000e5600000, 0x00000000e5700000|  0%| F|  |TAMS 0x00000000e5600000| PB 0x00000000e5600000| Untracked 
|  87|0x00000000e5700000, 0x00000000e5700000, 0x00000000e5800000|  0%| F|  |TAMS 0x00000000e5700000| PB 0x00000000e5700000| Untracked 
|  88|0x00000000e5800000, 0x00000000e5800000, 0x00000000e5900000|  0%| F|  |TAMS 0x00000000e5800000| PB 0x00000000e5800000| Untracked 
|  89|0x00000000e5900000, 0x00000000e5900000, 0x00000000e5a00000|  0%| F|  |TAMS 0x00000000e5900000| PB 0x00000000e5900000| Untracked 
|  90|0x00000000e5a00000, 0x00000000e5a00000, 0x00000000e5b00000|  0%| F|  |TAMS 0x00000000e5a00000| PB 0x00000000e5a00000| Untracked 
|  91|0x00000000e5b00000, 0x00000000e5b00000, 0x00000000e5c00000|  0%| F|  |TAMS 0x00000000e5b00000| PB 0x00000000e5b00000| Untracked 
|  92|0x00000000e5c00000, 0x00000000e5c00000, 0x00000000e5d00000|  0%| F|  |TAMS 0x00000000e5c00000| PB 0x00000000e5c00000| Untracked 
|  93|0x00000000e5d00000, 0x00000000e5d00000, 0x00000000e5e00000|  0%| F|  |TAMS 0x00000000e5d00000| PB 0x00000000e5d00000| Untracked 
|  94|0x00000000e5e00000, 0x00000000e5e00000, 0x00000000e5f00000|  0%| F|  |TAMS 0x00000000e5e00000| PB 0x00000000e5e00000| Untracked 
|  95|0x00000000e5f00000, 0x00000000e5f00000, 0x00000000e6000000|  0%| F|  |TAMS 0x00000000e5f00000| PB 0x00000000e5f00000| Untracked 
|  96|0x00000000e6000000, 0x00000000e6000000, 0x00000000e6100000|  0%| F|  |TAMS 0x00000000e6000000| PB 0x00000000e6000000| Untracked 
|  97|0x00000000e6100000, 0x00000000e6100000, 0x00000000e6200000|  0%| F|  |TAMS 0x00000000e6100000| PB 0x00000000e6100000| Untracked 
|  98|0x00000000e6200000, 0x00000000e6200000, 0x00000000e6300000|  0%| F|  |TAMS 0x00000000e6200000| PB 0x00000000e6200000| Untracked 
|  99|0x00000000e6300000, 0x00000000e6300000, 0x00000000e6400000|  0%| F|  |TAMS 0x00000000e6300000| PB 0x00000000e6300000| Untracked 
| 100|0x00000000e6400000, 0x00000000e6400000, 0x00000000e6500000|  0%| F|  |TAMS 0x00000000e6400000| PB 0x00000000e6400000| Untracked 
| 101|0x00000000e6500000, 0x00000000e6500000, 0x00000000e6600000|  0%| F|  |TAMS 0x00000000e6500000| PB 0x00000000e6500000| Untracked 
| 102|0x00000000e6600000, 0x00000000e6600000, 0x00000000e6700000|  0%| F|  |TAMS 0x00000000e6600000| PB 0x00000000e6600000| Untracked 
| 103|0x00000000e6700000, 0x00000000e6700000, 0x00000000e6800000|  0%| F|  |TAMS 0x00000000e6700000| PB 0x00000000e6700000| Untracked 
| 104|0x00000000e6800000, 0x00000000e6800000, 0x00000000e6900000|  0%| F|  |TAMS 0x00000000e6800000| PB 0x00000000e6800000| Untracked 
| 105|0x00000000e6900000, 0x00000000e6900000, 0x00000000e6a00000|  0%| F|  |TAMS 0x00000000e6900000| PB 0x00000000e6900000| Untracked 
| 106|0x00000000e6a00000, 0x00000000e6a00000, 0x00000000e6b00000|  0%| F|  |TAMS 0x00000000e6a00000| PB 0x00000000e6a00000| Untracked 
| 107|0x00000000e6b00000, 0x00000000e6b00000, 0x00000000e6c00000|  0%| F|  |TAMS 0x00000000e6b00000| PB 0x00000000e6b00000| Untracked 
| 108|0x00000000e6c00000, 0x00000000e6c00000, 0x00000000e6d00000|  0%| F|  |TAMS 0x00000000e6c00000| PB 0x00000000e6c00000| Untracked 
| 109|0x00000000e6d00000, 0x00000000e6d00000, 0x00000000e6e00000|  0%| F|  |TAMS 0x00000000e6d00000| PB 0x00000000e6d00000| Untracked 
| 110|0x00000000e6e00000, 0x00000000e6e00000, 0x00000000e6f00000|  0%| F|  |TAMS 0x00000000e6e00000| PB 0x00000000e6e00000| Untracked 
| 111|0x00000000e6f00000, 0x00000000e6f00000, 0x00000000e7000000|  0%| F|  |TAMS 0x00000000e6f00000| PB 0x00000000e6f00000| Untracked 
| 112|0x00000000e7000000, 0x00000000e7000000, 0x00000000e7100000|  0%| F|  |TAMS 0x00000000e7000000| PB 0x00000000e7000000| Untracked 
| 113|0x00000000e7100000, 0x00000000e7100000, 0x00000000e7200000|  0%| F|  |TAMS 0x00000000e7100000| PB 0x00000000e7100000| Untracked 
| 114|0x00000000e7200000, 0x00000000e7200000, 0x00000000e7300000|  0%| F|  |TAMS 0x00000000e7200000| PB 0x00000000e7200000| Untracked 
| 115|0x00000000e7300000, 0x00000000e7300000, 0x00000000e7400000|  0%| F|  |TAMS 0x00000000e7300000| PB 0x00000000e7300000| Untracked 
| 116|0x00000000e7400000, 0x00000000e7400000, 0x00000000e7500000|  0%| F|  |TAMS 0x00000000e7400000| PB 0x00000000e7400000| Untracked 
| 117|0x00000000e7500000, 0x00000000e7500000, 0x00000000e7600000|  0%| F|  |TAMS 0x00000000e7500000| PB 0x00000000e7500000| Untracked 
| 118|0x00000000e7600000, 0x00000000e7600000, 0x00000000e7700000|  0%| F|  |TAMS 0x00000000e7600000| PB 0x00000000e7600000| Untracked 
| 119|0x00000000e7700000, 0x00000000e7700000, 0x00000000e7800000|  0%| F|  |TAMS 0x00000000e7700000| PB 0x00000000e7700000| Untracked 
| 120|0x00000000e7800000, 0x00000000e7800000, 0x00000000e7900000|  0%| F|  |TAMS 0x00000000e7800000| PB 0x00000000e7800000| Untracked 
| 121|0x00000000e7900000, 0x00000000e7900000, 0x00000000e7a00000|  0%| F|  |TAMS 0x00000000e7900000| PB 0x00000000e7900000| Untracked 
| 122|0x00000000e7a00000, 0x00000000e7a00000, 0x00000000e7b00000|  0%| F|  |TAMS 0x00000000e7a00000| PB 0x00000000e7a00000| Untracked 
| 123|0x00000000e7b00000, 0x00000000e7b00000, 0x00000000e7c00000|  0%| F|  |TAMS 0x00000000e7b00000| PB 0x00000000e7b00000| Untracked 
| 124|0x00000000e7c00000, 0x00000000e7c00000, 0x00000000e7d00000|  0%| F|  |TAMS 0x00000000e7c00000| PB 0x00000000e7c00000| Untracked 
| 125|0x00000000e7d00000, 0x00000000e7d00000, 0x00000000e7e00000|  0%| F|  |TAMS 0x00000000e7d00000| PB 0x00000000e7d00000| Untracked 
| 126|0x00000000e7e00000, 0x00000000e7e00000, 0x00000000e7f00000|  0%| F|  |TAMS 0x00000000e7e00000| PB 0x00000000e7e00000| Untracked 
| 127|0x00000000e7f00000, 0x00000000e7f00000, 0x00000000e8000000|  0%| F|  |TAMS 0x00000000e7f00000| PB 0x00000000e7f00000| Untracked 
| 128|0x00000000e8000000, 0x00000000e8000000, 0x00000000e8100000|  0%| F|  |TAMS 0x00000000e8000000| PB 0x00000000e8000000| Untracked 
| 129|0x00000000e8100000, 0x00000000e8100000, 0x00000000e8200000|  0%| F|  |TAMS 0x00000000e8100000| PB 0x00000000e8100000| Untracked 
| 130|0x00000000e8200000, 0x00000000e8200000, 0x00000000e8300000|  0%| F|  |TAMS 0x00000000e8200000| PB 0x00000000e8200000| Untracked 
| 131|0x00000000e8300000, 0x00000000e8300000, 0x00000000e8400000|  0%| F|  |TAMS 0x00000000e8300000| PB 0x00000000e8300000| Untracked 
| 132|0x00000000e8400000, 0x00000000e8400000, 0x00000000e8500000|  0%| F|  |TAMS 0x00000000e8400000| PB 0x00000000e8400000| Untracked 
| 133|0x00000000e8500000, 0x00000000e8500000, 0x00000000e8600000|  0%| F|  |TAMS 0x00000000e8500000| PB 0x00000000e8500000| Untracked 
| 134|0x00000000e8600000, 0x00000000e8600000, 0x00000000e8700000|  0%| F|  |TAMS 0x00000000e8600000| PB 0x00000000e8600000| Untracked 
| 135|0x00000000e8700000, 0x00000000e8700000, 0x00000000e8800000|  0%| F|  |TAMS 0x00000000e8700000| PB 0x00000000e8700000| Untracked 
| 136|0x00000000e8800000, 0x00000000e8800000, 0x00000000e8900000|  0%| F|  |TAMS 0x00000000e8800000| PB 0x00000000e8800000| Untracked 
| 137|0x00000000e8900000, 0x00000000e8900000, 0x00000000e8a00000|  0%| F|  |TAMS 0x00000000e8900000| PB 0x00000000e8900000| Untracked 
| 138|0x00000000e8a00000, 0x00000000e8a00000, 0x00000000e8b00000|  0%| F|  |TAMS 0x00000000e8a00000| PB 0x00000000e8a00000| Untracked 
| 139|0x00000000e8b00000, 0x00000000e8b00000, 0x00000000e8c00000|  0%| F|  |TAMS 0x00000000e8b00000| PB 0x00000000e8b00000| Untracked 
| 140|0x00000000e8c00000, 0x00000000e8c00000, 0x00000000e8d00000|  0%| F|  |TAMS 0x00000000e8c00000| PB 0x00000000e8c00000| Untracked 
| 141|0x00000000e8d00000, 0x00000000e8d00000, 0x00000000e8e00000|  0%| F|  |TAMS 0x00000000e8d00000| PB 0x00000000e8d00000| Untracked 
| 142|0x00000000e8e00000, 0x00000000e8e00000, 0x00000000e8f00000|  0%| F|  |TAMS 0x00000000e8e00000| PB 0x00000000e8e00000| Untracked 
| 143|0x00000000e8f00000, 0x00000000e8f00000, 0x00000000e9000000|  0%| F|  |TAMS 0x00000000e8f00000| PB 0x00000000e8f00000| Untracked 
| 144|0x00000000e9000000, 0x00000000e9000000, 0x00000000e9100000|  0%| F|  |TAMS 0x00000000e9000000| PB 0x00000000e9000000| Untracked 
| 145|0x00000000e9100000, 0x00000000e9100000, 0x00000000e9200000|  0%| F|  |TAMS 0x00000000e9100000| PB 0x00000000e9100000| Untracked 
| 146|0x00000000e9200000, 0x00000000e9200000, 0x00000000e9300000|  0%| F|  |TAMS 0x00000000e9200000| PB 0x00000000e9200000| Untracked 
| 147|0x00000000e9300000, 0x00000000e9300000, 0x00000000e9400000|  0%| F|  |TAMS 0x00000000e9300000| PB 0x00000000e9300000| Untracked 
| 148|0x00000000e9400000, 0x00000000e9400000, 0x00000000e9500000|  0%| F|  |TAMS 0x00000000e9400000| PB 0x00000000e9400000| Untracked 
| 149|0x00000000e9500000, 0x00000000e9500000, 0x00000000e9600000|  0%| F|  |TAMS 0x00000000e9500000| PB 0x00000000e9500000| Untracked 
| 150|0x00000000e9600000, 0x00000000e9600000, 0x00000000e9700000|  0%| F|  |TAMS 0x00000000e9600000| PB 0x00000000e9600000| Untracked 
| 151|0x00000000e9700000, 0x00000000e9700000, 0x00000000e9800000|  0%| F|  |TAMS 0x00000000e9700000| PB 0x00000000e9700000| Untracked 
| 152|0x00000000e9800000, 0x00000000e9800000, 0x00000000e9900000|  0%| F|  |TAMS 0x00000000e9800000| PB 0x00000000e9800000| Untracked 
| 153|0x00000000e9900000, 0x00000000e9900000, 0x00000000e9a00000|  0%| F|  |TAMS 0x00000000e9900000| PB 0x00000000e9900000| Untracked 
| 154|0x00000000e9a00000, 0x00000000e9a00000, 0x00000000e9b00000|  0%| F|  |TAMS 0x00000000e9a00000| PB 0x00000000e9a00000| Untracked 
| 155|0x00000000e9b00000, 0x00000000e9b00000, 0x00000000e9c00000|  0%| F|  |TAMS 0x00000000e9b00000| PB 0x00000000e9b00000| Untracked 
| 156|0x00000000e9c00000, 0x00000000e9c00000, 0x00000000e9d00000|  0%| F|  |TAMS 0x00000000e9c00000| PB 0x00000000e9c00000| Untracked 
| 157|0x00000000e9d00000, 0x00000000e9d00000, 0x00000000e9e00000|  0%| F|  |TAMS 0x00000000e9d00000| PB 0x00000000e9d00000| Untracked 
| 158|0x00000000e9e00000, 0x00000000e9e00000, 0x00000000e9f00000|  0%| F|  |TAMS 0x00000000e9e00000| PB 0x00000000e9e00000| Untracked 
| 159|0x00000000e9f00000, 0x00000000e9f00000, 0x00000000ea000000|  0%| F|  |TAMS 0x00000000e9f00000| PB 0x00000000e9f00000| Untracked 
| 160|0x00000000ea000000, 0x00000000ea000000, 0x00000000ea100000|  0%| F|  |TAMS 0x00000000ea000000| PB 0x00000000ea000000| Untracked 
| 161|0x00000000ea100000, 0x00000000ea100000, 0x00000000ea200000|  0%| F|  |TAMS 0x00000000ea100000| PB 0x00000000ea100000| Untracked 
| 162|0x00000000ea200000, 0x00000000ea200000, 0x00000000ea300000|  0%| F|  |TAMS 0x00000000ea200000| PB 0x00000000ea200000| Untracked 
| 163|0x00000000ea300000, 0x00000000ea300000, 0x00000000ea400000|  0%| F|  |TAMS 0x00000000ea300000| PB 0x00000000ea300000| Untracked 
| 164|0x00000000ea400000, 0x00000000ea400000, 0x00000000ea500000|  0%| F|  |TAMS 0x00000000ea400000| PB 0x00000000ea400000| Untracked 
| 165|0x00000000ea500000, 0x00000000ea500000, 0x00000000ea600000|  0%| F|  |TAMS 0x00000000ea500000| PB 0x00000000ea500000| Untracked 
| 166|0x00000000ea600000, 0x00000000ea600000, 0x00000000ea700000|  0%| F|  |TAMS 0x00000000ea600000| PB 0x00000000ea600000| Untracked 
| 167|0x00000000ea700000, 0x00000000ea700000, 0x00000000ea800000|  0%| F|  |TAMS 0x00000000ea700000| PB 0x00000000ea700000| Untracked 
| 168|0x00000000ea800000, 0x00000000ea800000, 0x00000000ea900000|  0%| F|  |TAMS 0x00000000ea800000| PB 0x00000000ea800000| Untracked 
| 169|0x00000000ea900000, 0x00000000ea900000, 0x00000000eaa00000|  0%| F|  |TAMS 0x00000000ea900000| PB 0x00000000ea900000| Untracked 
| 170|0x00000000eaa00000, 0x00000000eaa00000, 0x00000000eab00000|  0%| F|  |TAMS 0x00000000eaa00000| PB 0x00000000eaa00000| Untracked 
| 171|0x00000000eab00000, 0x00000000eab00000, 0x00000000eac00000|  0%| F|  |TAMS 0x00000000eab00000| PB 0x00000000eab00000| Untracked 
| 172|0x00000000eac00000, 0x00000000eac00000, 0x00000000ead00000|  0%| F|  |TAMS 0x00000000eac00000| PB 0x00000000eac00000| Untracked 
| 173|0x00000000ead00000, 0x00000000ead00000, 0x00000000eae00000|  0%| F|  |TAMS 0x00000000ead00000| PB 0x00000000ead00000| Untracked 
| 174|0x00000000eae00000, 0x00000000eae00000, 0x00000000eaf00000|  0%| F|  |TAMS 0x00000000eae00000| PB 0x00000000eae00000| Untracked 
| 175|0x00000000eaf00000, 0x00000000eaf00000, 0x00000000eb000000|  0%| F|  |TAMS 0x00000000eaf00000| PB 0x00000000eaf00000| Untracked 
| 176|0x00000000eb000000, 0x00000000eb000000, 0x00000000eb100000|  0%| F|  |TAMS 0x00000000eb000000| PB 0x00000000eb000000| Untracked 
| 177|0x00000000eb100000, 0x00000000eb100000, 0x00000000eb200000|  0%| F|  |TAMS 0x00000000eb100000| PB 0x00000000eb100000| Untracked 
| 178|0x00000000eb200000, 0x00000000eb200000, 0x00000000eb300000|  0%| F|  |TAMS 0x00000000eb200000| PB 0x00000000eb200000| Untracked 
| 179|0x00000000eb300000, 0x00000000eb300000, 0x00000000eb400000|  0%| F|  |TAMS 0x00000000eb300000| PB 0x00000000eb300000| Untracked 
| 180|0x00000000eb400000, 0x00000000eb400000, 0x00000000eb500000|  0%| F|  |TAMS 0x00000000eb400000| PB 0x00000000eb400000| Untracked 
| 181|0x00000000eb500000, 0x00000000eb500000, 0x00000000eb600000|  0%| F|  |TAMS 0x00000000eb500000| PB 0x00000000eb500000| Untracked 
| 182|0x00000000eb600000, 0x00000000eb600000, 0x00000000eb700000|  0%| F|  |TAMS 0x00000000eb600000| PB 0x00000000eb600000| Untracked 
| 183|0x00000000eb700000, 0x00000000eb700000, 0x00000000eb800000|  0%| F|  |TAMS 0x00000000eb700000| PB 0x00000000eb700000| Untracked 
| 184|0x00000000eb800000, 0x00000000eb800000, 0x00000000eb900000|  0%| F|  |TAMS 0x00000000eb800000| PB 0x00000000eb800000| Untracked 
| 185|0x00000000eb900000, 0x00000000eb900000, 0x00000000eba00000|  0%| F|  |TAMS 0x00000000eb900000| PB 0x00000000eb900000| Untracked 
| 186|0x00000000eba00000, 0x00000000eba00000, 0x00000000ebb00000|  0%| F|  |TAMS 0x00000000eba00000| PB 0x00000000eba00000| Untracked 
| 187|0x00000000ebb00000, 0x00000000ebb00000, 0x00000000ebc00000|  0%| F|  |TAMS 0x00000000ebb00000| PB 0x00000000ebb00000| Untracked 
| 188|0x00000000ebc00000, 0x00000000ebc00000, 0x00000000ebd00000|  0%| F|  |TAMS 0x00000000ebc00000| PB 0x00000000ebc00000| Untracked 
| 189|0x00000000ebd00000, 0x00000000ebd00000, 0x00000000ebe00000|  0%| F|  |TAMS 0x00000000ebd00000| PB 0x00000000ebd00000| Untracked 
| 190|0x00000000ebe00000, 0x00000000ebe00000, 0x00000000ebf00000|  0%| F|  |TAMS 0x00000000ebe00000| PB 0x00000000ebe00000| Untracked 
| 191|0x00000000ebf00000, 0x00000000ebf00000, 0x00000000ec000000|  0%| F|  |TAMS 0x00000000ebf00000| PB 0x00000000ebf00000| Untracked 
| 192|0x00000000ec000000, 0x00000000ec000000, 0x00000000ec100000|  0%| F|  |TAMS 0x00000000ec000000| PB 0x00000000ec000000| Untracked 
| 193|0x00000000ec100000, 0x00000000ec100000, 0x00000000ec200000|  0%| F|  |TAMS 0x00000000ec100000| PB 0x00000000ec100000| Untracked 
| 194|0x00000000ec200000, 0x00000000ec200000, 0x00000000ec300000|  0%| F|  |TAMS 0x00000000ec200000| PB 0x00000000ec200000| Untracked 
| 195|0x00000000ec300000, 0x00000000ec300000, 0x00000000ec400000|  0%| F|  |TAMS 0x00000000ec300000| PB 0x00000000ec300000| Untracked 
| 196|0x00000000ec400000, 0x00000000ec400000, 0x00000000ec500000|  0%| F|  |TAMS 0x00000000ec400000| PB 0x00000000ec400000| Untracked 
| 197|0x00000000ec500000, 0x00000000ec500000, 0x00000000ec600000|  0%| F|  |TAMS 0x00000000ec500000| PB 0x00000000ec500000| Untracked 
| 198|0x00000000ec600000, 0x00000000ec600000, 0x00000000ec700000|  0%| F|  |TAMS 0x00000000ec600000| PB 0x00000000ec600000| Untracked 
| 199|0x00000000ec700000, 0x00000000ec700000, 0x00000000ec800000|  0%| F|  |TAMS 0x00000000ec700000| PB 0x00000000ec700000| Untracked 
| 200|0x00000000ec800000, 0x00000000ec800000, 0x00000000ec900000|  0%| F|  |TAMS 0x00000000ec800000| PB 0x00000000ec800000| Untracked 
| 201|0x00000000ec900000, 0x00000000ec900000, 0x00000000eca00000|  0%| F|  |TAMS 0x00000000ec900000| PB 0x00000000ec900000| Untracked 
| 202|0x00000000eca00000, 0x00000000eca00000, 0x00000000ecb00000|  0%| F|  |TAMS 0x00000000eca00000| PB 0x00000000eca00000| Untracked 
| 203|0x00000000ecb00000, 0x00000000ecb00000, 0x00000000ecc00000|  0%| F|  |TAMS 0x00000000ecb00000| PB 0x00000000ecb00000| Untracked 
| 204|0x00000000ecc00000, 0x00000000ecc29558, 0x00000000ecd00000| 16%| E|  |TAMS 0x00000000ecc00000| PB 0x00000000ecc00000| Complete 
| 205|0x00000000ecd00000, 0x00000000ece00000, 0x00000000ece00000|100%| E|CS|TAMS 0x00000000ecd00000| PB 0x00000000ecd00000| Complete 
| 206|0x00000000ece00000, 0x00000000ecf00000, 0x00000000ecf00000|100%| E|CS|TAMS 0x00000000ece00000| PB 0x00000000ece00000| Complete 
| 207|0x00000000ecf00000, 0x00000000ed000000, 0x00000000ed000000|100%| E|CS|TAMS 0x00000000ecf00000| PB 0x00000000ecf00000| Complete 
| 208|0x00000000ed000000, 0x00000000ed100000, 0x00000000ed100000|100%| E|CS|TAMS 0x00000000ed000000| PB 0x00000000ed000000| Complete 
| 209|0x00000000ed100000, 0x00000000ed200000, 0x00000000ed200000|100%| E|CS|TAMS 0x00000000ed100000| PB 0x00000000ed100000| Complete 
| 210|0x00000000ed200000, 0x00000000ed300000, 0x00000000ed300000|100%| E|CS|TAMS 0x00000000ed200000| PB 0x00000000ed200000| Complete 
| 211|0x00000000ed300000, 0x00000000ed400000, 0x00000000ed400000|100%| E|CS|TAMS 0x00000000ed300000| PB 0x00000000ed300000| Complete 
| 212|0x00000000ed400000, 0x00000000ed500000, 0x00000000ed500000|100%| E|CS|TAMS 0x00000000ed400000| PB 0x00000000ed400000| Complete 
| 213|0x00000000ed500000, 0x00000000ed600000, 0x00000000ed600000|100%| E|CS|TAMS 0x00000000ed500000| PB 0x00000000ed500000| Complete 
| 214|0x00000000ed600000, 0x00000000ed700000, 0x00000000ed700000|100%| E|CS|TAMS 0x00000000ed600000| PB 0x00000000ed600000| Complete 
| 215|0x00000000ed700000, 0x00000000ed800000, 0x00000000ed800000|100%| E|CS|TAMS 0x00000000ed700000| PB 0x00000000ed700000| Complete 
| 216|0x00000000ed800000, 0x00000000ed900000, 0x00000000ed900000|100%| E|CS|TAMS 0x00000000ed800000| PB 0x00000000ed800000| Complete 
| 217|0x00000000ed900000, 0x00000000eda00000, 0x00000000eda00000|100%| E|CS|TAMS 0x00000000ed900000| PB 0x00000000ed900000| Complete 
| 218|0x00000000eda00000, 0x00000000edb00000, 0x00000000edb00000|100%| E|CS|TAMS 0x00000000eda00000| PB 0x00000000eda00000| Complete 
| 219|0x00000000edb00000, 0x00000000edc00000, 0x00000000edc00000|100%| E|CS|TAMS 0x00000000edb00000| PB 0x00000000edb00000| Complete 
| 220|0x00000000edc00000, 0x00000000edd00000, 0x00000000edd00000|100%| E|CS|TAMS 0x00000000edc00000| PB 0x00000000edc00000| Complete 
| 221|0x00000000edd00000, 0x00000000ede00000, 0x00000000ede00000|100%| E|CS|TAMS 0x00000000edd00000| PB 0x00000000edd00000| Complete 
| 222|0x00000000ede00000, 0x00000000edf00000, 0x00000000edf00000|100%| E|CS|TAMS 0x00000000ede00000| PB 0x00000000ede00000| Complete 
| 223|0x00000000edf00000, 0x00000000ee000000, 0x00000000ee000000|100%| E|CS|TAMS 0x00000000edf00000| PB 0x00000000edf00000| Complete 
| 224|0x00000000ee000000, 0x00000000ee100000, 0x00000000ee100000|100%| E|CS|TAMS 0x00000000ee000000| PB 0x00000000ee000000| Complete 
| 225|0x00000000ee100000, 0x00000000ee200000, 0x00000000ee200000|100%| E|CS|TAMS 0x00000000ee100000| PB 0x00000000ee100000| Complete 
| 226|0x00000000ee200000, 0x00000000ee300000, 0x00000000ee300000|100%| E|CS|TAMS 0x00000000ee200000| PB 0x00000000ee200000| Complete 
| 227|0x00000000ee300000, 0x00000000ee400000, 0x00000000ee400000|100%| E|CS|TAMS 0x00000000ee300000| PB 0x00000000ee300000| Complete 
| 228|0x00000000ee400000, 0x00000000ee500000, 0x00000000ee500000|100%| E|CS|TAMS 0x00000000ee400000| PB 0x00000000ee400000| Complete 
| 229|0x00000000ee500000, 0x00000000ee600000, 0x00000000ee600000|100%| E|CS|TAMS 0x00000000ee500000| PB 0x00000000ee500000| Complete 
| 230|0x00000000ee600000, 0x00000000ee700000, 0x00000000ee700000|100%| E|CS|TAMS 0x00000000ee600000| PB 0x00000000ee600000| Complete 
| 231|0x00000000ee700000, 0x00000000ee800000, 0x00000000ee800000|100%| E|CS|TAMS 0x00000000ee700000| PB 0x00000000ee700000| Complete 
| 232|0x00000000ee800000, 0x00000000ee900000, 0x00000000ee900000|100%| E|CS|TAMS 0x00000000ee800000| PB 0x00000000ee800000| Complete 
| 233|0x00000000ee900000, 0x00000000eea00000, 0x00000000eea00000|100%| E|CS|TAMS 0x00000000ee900000| PB 0x00000000ee900000| Complete 
| 234|0x00000000eea00000, 0x00000000eeb00000, 0x00000000eeb00000|100%| E|CS|TAMS 0x00000000eea00000| PB 0x00000000eea00000| Complete 
| 235|0x00000000eeb00000, 0x00000000eec00000, 0x00000000eec00000|100%| E|CS|TAMS 0x00000000eeb00000| PB 0x00000000eeb00000| Complete 
| 236|0x00000000eec00000, 0x00000000eed00000, 0x00000000eed00000|100%| E|CS|TAMS 0x00000000eec00000| PB 0x00000000eec00000| Complete 
| 237|0x00000000eed00000, 0x00000000eee00000, 0x00000000eee00000|100%| S|CS|TAMS 0x00000000eed00000| PB 0x00000000eed00000| Complete 
| 238|0x00000000eee00000, 0x00000000eef00000, 0x00000000eef00000|100%| S|CS|TAMS 0x00000000eee00000| PB 0x00000000eee00000| Complete 
| 239|0x00000000eef00000, 0x00000000ef000000, 0x00000000ef000000|100%| S|CS|TAMS 0x00000000eef00000| PB 0x00000000eef00000| Complete 
| 240|0x00000000ef000000, 0x00000000ef100000, 0x00000000ef100000|100%| S|CS|TAMS 0x00000000ef000000| PB 0x00000000ef000000| Complete 
| 241|0x00000000ef100000, 0x00000000ef200000, 0x00000000ef200000|100%| S|CS|TAMS 0x00000000ef100000| PB 0x00000000ef100000| Complete 
| 242|0x00000000ef200000, 0x00000000ef300000, 0x00000000ef300000|100%| S|CS|TAMS 0x00000000ef200000| PB 0x00000000ef200000| Complete 
| 243|0x00000000ef300000, 0x00000000ef400000, 0x00000000ef400000|100%| S|CS|TAMS 0x00000000ef300000| PB 0x00000000ef300000| Complete 
| 244|0x00000000ef400000, 0x00000000ef500000, 0x00000000ef500000|100%| E|  |TAMS 0x00000000ef400000| PB 0x00000000ef400000| Complete 
| 245|0x00000000ef500000, 0x00000000ef600000, 0x00000000ef600000|100%| E|CS|TAMS 0x00000000ef500000| PB 0x00000000ef500000| Complete 
| 246|0x00000000ef600000, 0x00000000ef700000, 0x00000000ef700000|100%| E|CS|TAMS 0x00000000ef600000| PB 0x00000000ef600000| Complete 
| 247|0x00000000ef700000, 0x00000000ef800000, 0x00000000ef800000|100%| E|CS|TAMS 0x00000000ef700000| PB 0x00000000ef700000| Complete 
| 248|0x00000000ef800000, 0x00000000ef900000, 0x00000000ef900000|100%| E|CS|TAMS 0x00000000ef800000| PB 0x00000000ef800000| Complete 
| 249|0x00000000ef900000, 0x00000000efa00000, 0x00000000efa00000|100%| E|CS|TAMS 0x00000000ef900000| PB 0x00000000ef900000| Complete 
| 250|0x00000000efa00000, 0x00000000efb00000, 0x00000000efb00000|100%| E|CS|TAMS 0x00000000efa00000| PB 0x00000000efa00000| Complete 
| 251|0x00000000efb00000, 0x00000000efc00000, 0x00000000efc00000|100%| E|CS|TAMS 0x00000000efb00000| PB 0x00000000efb00000| Complete 
| 252|0x00000000efc00000, 0x00000000efd00000, 0x00000000efd00000|100%| E|CS|TAMS 0x00000000efc00000| PB 0x00000000efc00000| Complete 
| 253|0x00000000efd00000, 0x00000000efe00000, 0x00000000efe00000|100%| S|CS|TAMS 0x00000000efd00000| PB 0x00000000efd00000| Complete 
| 254|0x00000000efe00000, 0x00000000eff00000, 0x00000000eff00000|100%| S|CS|TAMS 0x00000000efe00000| PB 0x00000000efe00000| Complete 
| 255|0x00000000eff00000, 0x00000000f0000000, 0x00000000f0000000|100%| E|CS|TAMS 0x00000000eff00000| PB 0x00000000eff00000| Complete 

Card table byte_map: [0x000001944e520000,0x000001944e620000] _byte_map_base: 0x000001944de20000

Marking Bits: (CMBitMap*) 0x00000194364ea1e0
 Bits: [0x000001944e620000, 0x000001944ee20000)

Polling page: 0x0000019435ac0000

Metaspace:

Usage:
  Non-class:     33.69 MB used.
      Class:      5.43 MB used.
       Both:     39.12 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      34.31 MB ( 54%) committed,  1 nodes.
      Class space:      320.00 MB reserved,       6.06 MB (  2%) committed,  1 nodes.
             Both:      384.00 MB reserved,      40.38 MB ( 11%) committed. 

Chunk freelists:
   Non-Class:  13.08 MB
       Class:  9.88 MB
        Both:  22.95 MB

MaxMetaspaceSize: 384.00 MB
CompressedClassSpaceSize: 320.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.69 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 2268.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 646.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 4083.
num_chunk_merges: 6.
num_chunk_splits: 2572.
num_chunks_enlarged: 1512.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=2422Kb max_used=2577Kb free=116745Kb
 bounds [0x0000019446730000, 0x00000194469c0000, 0x000001944db90000]
CodeHeap 'profiled nmethods': size=119104Kb used=8913Kb max_used=8984Kb free=110190Kb
 bounds [0x000001943eb90000, 0x000001943f460000, 0x0000019445fe0000]
CodeHeap 'non-nmethods': size=7488Kb used=2680Kb max_used=3207Kb free=4807Kb
 bounds [0x0000019445fe0000, 0x0000019446360000, 0x0000019446730000]
 total_blobs=5287 nmethods=4460 adapters=730
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 157.008 Thread 0x000001946706aca0 4955       3       sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl::validateConstructorArguments (82 bytes)
Event: 157.011 Thread 0x000001946706aca0 nmethod 4955 0x000001943ec92e90 code [0x000001943ec93220, 0x000001943ec94c10]
Event: 157.012 Thread 0x000001946706aca0 4953       3       java.util.Hashtable$Enumerator::hasMoreElements (53 bytes)
Event: 157.012 Thread 0x000001946706aca0 nmethod 4953 0x000001943eeefd90 code [0x000001943eeeff40, 0x000001943eef01f8]
Event: 157.012 Thread 0x000001946706aca0 4954       3       java.util.Hashtable$Enumerator::hasNext (5 bytes)
Event: 157.013 Thread 0x000001946706aca0 nmethod 4954 0x000001943eeefa10 code [0x000001943eeefbc0, 0x000001943eeefd00]
Event: 157.013 Thread 0x000001946706aca0 4956       3       java.util.Hashtable$Enumerator::nextElement (111 bytes)
Event: 157.014 Thread 0x000001946706aca0 nmethod 4956 0x000001943ec92610 code [0x000001943ec92800, 0x000001943ec92cf0]
Event: 157.014 Thread 0x000001946706aca0 4957       3       java.util.Hashtable$Enumerator::next (27 bytes)
Event: 157.015 Thread 0x000001946706aca0 nmethod 4957 0x000001943eeef210 code [0x000001943eeef3e0, 0x000001943eeef648]
Event: 157.015 Thread 0x000001946706aca0 4958       3       org.codehaus.groovy.reflection.CachedClass::getName (8 bytes)
Event: 157.016 Thread 0x000001946706aca0 nmethod 4958 0x000001943ec92110 code [0x000001943ec922c0, 0x000001943ec924c8]
Event: 157.016 Thread 0x000001946706aca0 4949       3       java.util.Collections$UnmodifiableMap$UnmodifiableEntrySet$1::next (5 bytes)
Event: 157.017 Thread 0x000001946706aca0 nmethod 4949 0x000001943ec91590 code [0x000001943ec917a0, 0x000001943ec91ee0]
Event: 157.017 Thread 0x000001946706aca0 4950       3       java.util.Collections$UnmodifiableMap$UnmodifiableEntrySet$1::next (20 bytes)
Event: 157.017 Thread 0x000001946706aca0 nmethod 4950 0x000001943ec90b10 code [0x000001943ec90d20, 0x000001943ec913e8]
Event: 157.017 Thread 0x000001946706aca0 4951       3       java.util.Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry::<init> (16 bytes)
Event: 157.018 Thread 0x000001946706aca0 nmethod 4951 0x000001943ec90390 code [0x000001943ec90560, 0x000001943ec90988]
Event: 157.018 Thread 0x000001946706aca0 4952       3       java.util.Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry::getKey (10 bytes)
Event: 157.018 Thread 0x000001946706aca0 nmethod 4952 0x000001943ec8ff10 code [0x000001943ec900c0, 0x000001943ec90298]

GC Heap History (14 events):
Event: 45.887 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 262144K, used 26624K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 1125K, committed 1280K, reserved 393216K
  class space    used 75K, committed 128K, reserved 327680K
}
Event: 45.929 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 262144K, used 10378K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 1125K, committed 1280K, reserved 393216K
  class space    used 75K, committed 128K, reserved 327680K
}
Event: 53.436 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 262144K, used 40074K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 3 survivors (3072K)
 Metaspace       used 4363K, committed 4544K, reserved 393216K
  class space    used 528K, committed 640K, reserved 327680K
}
Event: 53.458 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 262144K, used 12080K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 4363K, committed 4544K, reserved 393216K
  class space    used 528K, committed 640K, reserved 327680K
}
Event: 120.566 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 262144K, used 55088K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 2 survivors (2048K)
 Metaspace       used 5085K, committed 5312K, reserved 393216K
  class space    used 646K, committed 768K, reserved 327680K
}
Event: 120.575 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 262144K, used 17437K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 5085K, committed 5312K, reserved 393216K
  class space    used 646K, committed 768K, reserved 327680K
}
Event: 136.000 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 262144K, used 104477K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 92 young (94208K), 6 survivors (6144K)
 Metaspace       used 20912K, committed 21504K, reserved 393216K
  class space    used 2951K, committed 3264K, reserved 327680K
}
Event: 136.025 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 262144K, used 24605K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 20912K, committed 21504K, reserved 393216K
  class space    used 2951K, committed 3264K, reserved 327680K
}
Event: 152.422 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 262144K, used 131101K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 119 young (121856K), 13 survivors (13312K)
 Metaspace       used 34615K, committed 35968K, reserved 393216K
  class space    used 5040K, committed 5760K, reserved 327680K
}
Event: 152.453 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 262144K, used 33035K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 34615K, committed 35968K, reserved 393216K
  class space    used 5040K, committed 5760K, reserved 327680K
}
Event: 153.310 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 262144K, used 35083K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 16 survivors (16384K)
 Metaspace       used 34971K, committed 36288K, reserved 393216K
  class space    used 5081K, committed 5760K, reserved 327680K
}
Event: 153.331 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 262144K, used 33283K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 34971K, committed 36288K, reserved 393216K
  class space    used 5081K, committed 5760K, reserved 327680K
}
Event: 153.395 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 262144K, used 33283K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 9 survivors (9216K)
 Metaspace       used 35319K, committed 36608K, reserved 393216K
  class space    used 5104K, committed 5760K, reserved 327680K
}
Event: 153.410 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 262144K, used 33331K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 35319K, committed 36608K, reserved 393216K
  class space    used 5104K, committed 5760K, reserved 327680K
}

Dll operation events (15 events):
Event: 0.277 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
Event: 0.900 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
Event: 1.271 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 1.300 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
Event: 1.357 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
Event: 1.393 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
Event: 1.449 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 27.836 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
Event: 47.039 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
Event: 47.732 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 47.866 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 123.156 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
Event: 123.236 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
Event: 124.799 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
Event: 126.028 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 156.323 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.323 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.330 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.330 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.340 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.341 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.368 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.368 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.375 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.375 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.380 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x000001943ebaf4c0 sp=0x0000000adcaf7770
Event: 156.381 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x0000019446034e42 sp=0x0000000adcaf6c60 mode 0
Event: 156.483 Thread 0x000001946ce70cf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000194467aba54 relative=0x00000000000012b4
Event: 156.483 Thread 0x000001946ce70cf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000194467aba54 method=jdk.internal.loader.BuiltinClassLoader.findLoadedModule(Ljava/lang/String;)Ljdk/internal/loader/BuiltinClassLoader$LoadedModule; @ 8 c2
Event: 156.483 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x00000194467aba54 sp=0x0000000adcaf8010
Event: 156.483 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x00000194460346a2 sp=0x0000000adcaf7f68 mode 2
Event: 156.504 Thread 0x000001946ce70cf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019446758704 relative=0x00000000000009c4
Event: 156.504 Thread 0x000001946ce70cf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019446758704 method=jdk.internal.loader.BuiltinClassLoader.findLoadedModule(Ljava/lang/String;)Ljdk/internal/loader/BuiltinClassLoader$LoadedModule; @ 8 c2
Event: 156.504 Thread 0x000001946ce70cf0 DEOPT PACKING pc=0x0000019446758704 sp=0x0000000adcaf8070
Event: 156.504 Thread 0x000001946ce70cf0 DEOPT UNPACKING pc=0x00000194460346a2 sp=0x0000000adcaf8050 mode 2

Classes loaded (20 events):
Event: 156.089 Loading class jdk/internal/jrtfs/JrtDirectoryStream
Event: 156.091 Loading class jdk/internal/jrtfs/JrtDirectoryStream done
Event: 156.096 Loading class jdk/internal/jrtfs/JrtDirectoryStream$1
Event: 156.096 Loading class jdk/internal/jrtfs/JrtDirectoryStream$1 done
Event: 156.099 Loading class java/util/function/BooleanSupplier
Event: 156.100 Loading class java/util/function/BooleanSupplier done
Event: 156.103 Loading class jdk/internal/jimage/ImageReader$Resource
Event: 156.104 Loading class jdk/internal/jimage/ImageReader$Resource done
Event: 156.405 Loading class java/util/WeakHashMap$KeyIterator
Event: 156.406 Loading class java/util/WeakHashMap$HashIterator
Event: 156.407 Loading class java/util/WeakHashMap$HashIterator done
Event: 156.407 Loading class java/util/WeakHashMap$KeyIterator done
Event: 156.691 Loading class com/sun/beans/finder/MethodFinder
Event: 156.692 Loading class com/sun/beans/finder/AbstractFinder
Event: 156.693 Loading class com/sun/beans/finder/AbstractFinder done
Event: 156.693 Loading class com/sun/beans/finder/MethodFinder done
Event: 156.693 Loading class com/sun/beans/finder/MethodFinder$1
Event: 156.693 Loading class com/sun/beans/finder/MethodFinder$1 done
Event: 156.694 Loading class com/sun/beans/finder/FinderUtils
Event: 156.694 Loading class com/sun/beans/finder/FinderUtils done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 156.486 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed5dd290}: groovy/lang/ScriptBeanInfo> (0x00000000ed5dd290) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.486 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed5e64c0}: groovy/lang/ScriptCustomizer> (0x00000000ed5e64c0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.492 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed5f7fc8}: org/gradle/groovy/scripts/ScriptCustomizer> (0x00000000ed5f7fc8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.494 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed408360}: org/gradle/groovy/scripts/BasicScriptCustomizer> (0x00000000ed408360) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.497 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed41eaa8}: org/gradle/groovy/scripts/DefaultScriptCustomizer> (0x00000000ed41eaa8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.501 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed4452a0}: org/gradle/initialization/InitScriptCustomizer> (0x00000000ed4452a0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.504 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed454100}: init_7zixjy1o65m73wwvyfpvhfovqCustomizer> (0x00000000ed454100) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.576 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed30cf90}: org/gradle/internal/extensibility/DefaultExtraPropertiesExtensionBeanInfo> (0x00000000ed30cf90) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.577 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed315598}: org/gradle/internal/extensibility/DefaultExtraPropertiesExtensionCustomizer> (0x00000000ed315598) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.665 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed3c26d8}: groovy/lang/Closure$1BeanInfo> (0x00000000ed3c26d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.668 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed3d03f0}: groovy/lang/ClosureBeanInfo> (0x00000000ed3d03f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.671 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed3ddb10}: groovy/lang/ClosureCustomizer> (0x00000000ed3ddb10) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.690 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ed20ea50}: groovy/lang/Closure$1Customizer> (0x00000000ed20ea50) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.833 Thread 0x000001946ce70cf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ed2efd18}: 'void java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ed2efd18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 156.944 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf17788}: org/gradle/invocation/DefaultGradle_DecoratedBeanInfo> (0x00000000ecf17788) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.951 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf265c0}: org/gradle/invocation/DefaultGradleBeanInfo> (0x00000000ecf265c0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.957 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf358c8}: org/gradle/api/internal/project/AbstractPluginAwareBeanInfo> (0x00000000ecf358c8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.965 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf44c70}: org/gradle/api/internal/project/AbstractPluginAwareCustomizer> (0x00000000ecf44c70) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 156.992 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf5a548}: org/gradle/invocation/DefaultGradleCustomizer> (0x00000000ecf5a548) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 157.003 Thread 0x000001946ce70cf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ecf9ede8}: org/gradle/invocation/DefaultGradle_DecoratedCustomizer> (0x00000000ecf9ede8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 153.469 Executing VM operation: G1PauseCleanup
Event: 153.479 Executing VM operation: G1PauseCleanup done
Event: 153.576 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 153.576 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 154.585 Executing VM operation: Cleanup
Event: 154.586 Executing VM operation: Cleanup done
Event: 154.997 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 154.997 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 155.758 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 155.759 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 155.759 Executing VM operation: Cleanup
Event: 155.759 Executing VM operation: Cleanup done
Event: 155.784 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 155.785 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 155.846 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 155.847 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 156.087 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 156.087 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 157.093 Executing VM operation: Cleanup
Event: 157.093 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ebed990
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ebee710
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec0e110
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec0e610
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec0eb90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec10d10
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec11c90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec40a90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec42890
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec43590
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec43a10
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec44a90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec7d190
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec7d610
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec7fe90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ec84910
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ecc8790
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943ecca490
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943eccef90
Event: 153.458 Thread 0x000001946700a190 flushing  nmethod 0x000001943f0d6190

Events (20 events):
Event: 138.038 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce70660
Event: 138.933 Thread 0x000001946ce73450 Thread added: 0x000001946ce76f60
Event: 139.150 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce75520
Event: 139.171 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce775f0
Event: 139.894 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce74800
Event: 139.896 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce74170
Event: 139.897 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce76240
Event: 140.412 Thread 0x000001946ca6e260 Thread exited: 0x000001946ca6e260
Event: 140.498 Thread 0x000001946706aca0 Thread added: 0x000001946c85a3c0
Event: 140.549 Thread 0x000001946706aca0 Thread added: 0x000001946c859620
Event: 141.439 Thread 0x000001946c85a3c0 Thread exited: 0x000001946c85a3c0
Event: 142.159 Thread 0x000001946c859620 Thread added: 0x000001946c85a3c0
Event: 142.315 Thread 0x000001946c85a3c0 Thread exited: 0x000001946c85a3c0
Event: 143.538 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce74e90
Event: 143.672 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce75bb0
Event: 145.020 Thread 0x000001946ce70cf0 Thread added: 0x000001946ce768d0
Event: 146.582 Thread 0x000001946c859620 Thread exited: 0x000001946c859620
Event: 146.716 Thread 0x000001946ce70cf0 Thread added: 0x000001946e7d5c30
Event: 148.635 Thread 0x000001946ce70cf0 Thread added: 0x000001946e7d41f0
Event: 149.353 Thread 0x000001946ce70cf0 Thread added: 0x000001946e7d62c0


Dynamic libraries:
0x00007ff794cf0000 - 0x00007ff794cfe000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe
0x00007ffe23080000 - 0x00007ffe232e6000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdf9f40000 - 0x00007ffdf9f5b000 	C:\Program Files\Norton\Suite\aswhook.dll
0x00007ffe21cd0000 - 0x00007ffe21d99000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe20830000 - 0x00007ffe20bfc000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe1cf70000 - 0x00007ffe1d00e000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffe20210000 - 0x00007ffe2035b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe0c710000 - 0x00007ffe0c72e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\VCRUNTIME140.dll
0x00007ffe0fed0000 - 0x00007ffe0fee8000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jli.dll
0x00007ffe22e70000 - 0x00007ffe2303a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe206d0000 - 0x00007ffe206f7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe01860000 - 0x00007ffe01afa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffe221f0000 - 0x00007ffe2221b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe22540000 - 0x00007ffe225e9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe20360000 - 0x00007ffe20492000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe20620000 - 0x00007ffe206c3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe214a0000 - 0x00007ffe214d0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe0fec0000 - 0x00007ffe0fecc000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\vcruntime140_1.dll
0x00007ffdf8a20000 - 0x00007ffdf8aad000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\msvcp140.dll
0x00007ffd803b0000 - 0x00007ffd81140000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server\jvm.dll
0x00007ffe22480000 - 0x00007ffe22532000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe22250000 - 0x00007ffe222f6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe220c0000 - 0x00007ffe221d6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe225f0000 - 0x00007ffe22664000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe1fea0000 - 0x00007ffe1fefe000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe179e0000 - 0x00007ffe17a16000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe1dbc0000 - 0x00007ffe1dbcb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe1fe80000 - 0x00007ffe1fe94000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe1efd0000 - 0x00007ffe1efea000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe0ca30000 - 0x00007ffe0ca3a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
0x00007ffe1a7b0000 - 0x00007ffe1a9f1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe20f20000 - 0x00007ffe212a4000 	C:\WINDOWS\System32\combase.dll
0x00007ffe212b0000 - 0x00007ffe21390000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe13090000 - 0x00007ffe130c9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe20700000 - 0x00007ffe20799000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe04b00000 - 0x00007ffe04b0f000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
0x00007ffe00d10000 - 0x00007ffe00d2f000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
0x00007ffe22740000 - 0x00007ffe22e6d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe204a0000 - 0x00007ffe20614000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffe1de50000 - 0x00007ffe1e6a6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe213a0000 - 0x00007ffe2148f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe22670000 - 0x00007ffe226d9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe20120000 - 0x00007ffe2014f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdd4760000 - 0x00007ffdd4837000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
0x00007ffe01c00000 - 0x00007ffe01c18000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
0x00007ffe0d5a0000 - 0x00007ffe0d5b0000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
0x00007ffe19530000 - 0x00007ffe1964e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe1f540000 - 0x00007ffe1f5aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe01b90000 - 0x00007ffe01ba6000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
0x00007ffe00d00000 - 0x00007ffe00d10000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
0x00007ffda4370000 - 0x00007ffda4397000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffdd45c0000 - 0x00007ffdd4704000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffdf8a10000 - 0x00007ffdf8a1a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
0x00007ffde33d0000 - 0x00007ffde33db000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
0x00007ffe221e0000 - 0x00007ffe221e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe1f7f0000 - 0x00007ffe1f80c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe1ef30000 - 0x00007ffe1ef6a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe1f5e0000 - 0x00007ffe1f60b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe200f0000 - 0x00007ffe20116000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffe1f7e0000 - 0x00007ffe1f7ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffe1ea80000 - 0x00007ffe1eab3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe22730000 - 0x00007ffe2273a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffde3380000 - 0x00007ffde3389000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
0x00007ffde2b70000 - 0x00007ffde2b7e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll
0x00007ffe20cc0000 - 0x00007ffe20e37000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffe1f9f0000 - 0x00007ffe1fa20000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffe1f9a0000 - 0x00007ffe1f9df000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffe1acc0000 - 0x00007ffe1acc8000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Norton\Suite;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -XX:MaxMetaspaceSize=384m -XX:+HeapDumpOnOutOfMemoryError -Xms256m -Xmx512m -Dfile.encoding=utf8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\gradle-daemon-main-8.9.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 335544320                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 268435456                                 {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 536870912                                 {product} {command line}
   size_t MaxMetaspaceSize                         = 402653184                                 {product} {command line}
   size_t MaxNewSize                               = 321912832                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 268435456                                 {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 536870912                              {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
PATH=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;;C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=gokul
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 1 days 21:55 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x8600106, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, rdpid, f16c
Processor Information for the first 16 processors :
  Max Mhz: 2901, Current Mhz: 2901, Mhz Limit: 2901

Memory: 4k page, system-wide physical 7599M (147M free)
TotalPageFile size 31151M (AvailPageFile size 12M)
current process WorkingSet (physical memory assigned to process): 228M, peak: 234M
current process commit charge ("private bytes"): 454M, peak: 460M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
