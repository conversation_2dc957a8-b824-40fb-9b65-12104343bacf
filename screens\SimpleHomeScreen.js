import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const SimpleHomeScreen = ({ navigation }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  const handleNavigation = (screen) => {
    try {
      console.log('Navigating to:', screen);
      navigation.navigate(screen);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.homeContainer}>
          <Animated.View style={[styles.logoContainer, { transform: [{ scale: scaleAnim }] }]}>
            <Text style={styles.logoText}>🐝</Text>
          </Animated.View>
          
          <Animated.Text style={[styles.welcomeText, { opacity: fadeAnim }]}>
            Welcome to BeeTech!
          </Animated.Text>
          
          <Animated.Text style={[styles.subtitleText, { opacity: fadeAnim }]}>
            Enhance your knowledge with fun quizzes!
          </Animated.Text>

          <Animated.View style={[styles.buttonContainer, { opacity: fadeAnim }]}>
            <TouchableOpacity 
              onPress={() => handleNavigation('Settings')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="settings" size={24} color="#fff" />
              <Text style={styles.buttonText}>Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => handleNavigation('Quiz')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="game-controller" size={24} color="#fff" />
              <Text style={styles.buttonText}>Start Quiz</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => handleNavigation('AiChat')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="chatbubbles-outline" size={24} color="#fff" />
              <Text style={styles.buttonText}>Ask AI</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => handleNavigation('StudyTools')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="library-outline" size={24} color="#fff" />
              <Text style={styles.buttonText}>Study Tools</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => handleNavigation('Favorites')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="heart-outline" size={24} color="#fff" />
              <Text style={styles.buttonText}>Favorites</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => handleNavigation('Notes')} 
              style={styles.button} 
              activeOpacity={0.7}
            >
              <Ionicons name="document-text-outline" size={24} color="#fff" />
              <Text style={styles.buttonText}>My Notes</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  homeContainer: {
    alignItems: 'center',
    padding: 20,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
  },
  logoText: {
    fontSize: 60,
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitleText: {
    fontSize: 18,
    color: '#f0f0f0',
    marginBottom: 30,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff9800',
    paddingVertical: 16,
    paddingHorizontal: 28,
    borderRadius: 14,
    marginBottom: 16,
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    elevation: 7,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 12,
  },
});

export default SimpleHomeScreen;
