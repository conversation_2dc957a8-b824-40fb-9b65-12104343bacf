// app.config.js
const withAdIdPermission = require('./plugins/with-ad-id-permission');

export default ({ config }) => ({
  ...config,
  'react-native-google-mobile-ads': {
    androidAppId: 'ca-app-pub-9706687137550019~9208363455',
    iosAppId: 'ca-app-pub-9706687137550019~9208363455'
  },
  plugins: [
    ...(config.plugins || []),
    withAdIdPermission,
    [
      'react-native-google-mobile-ads',
      {
        androidAppId: 'ca-app-pub-9706687137550019~9208363455',
        iosAppId: 'ca-app-pub-9706687137550019~9208363455'
      }
    ]
  ],
  android: {
    ...(config.android || {}),
    permissions: [
      ...(config.android?.permissions || []),
    ]
  }
});
