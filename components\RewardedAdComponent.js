import React, { useEffect, useRef, useState } from 'react';
import { Alert } from 'react-native';
import AdManager from '../src/services/AdManager';
import { AD_PLACEMENTS } from '../src/config/adConfig';

// Try to import native ads, fall back gracefully if not available
let RewardedAd, AdEventType, RewardedAdEventType;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  RewardedAd = GoogleMobileAds.RewardedAd;
  AdEventType = GoogleMobileAds.AdEventType;
  RewardedAdEventType = GoogleMobileAds.RewardedAdEventType;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, rewarded ads disabled');
  RewardedAd = null;
  AdEventType = null;
  RewardedAdEventType = null;
}

const RewardedAdComponent = ({
  placement = AD_PLACEMENTS.HINT_REWARDED,
  onAdLoaded = () => {},
  onAdFailedToLoad = () => {},
  onUserEarnedReward = () => {},
  onAdDismissed = () => {},
  autoLoad = true
}) => {
  const rewardedRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isShowing, setIsShowing] = useState(false);

  const unitId = AdManager.getAdUnitForPlacement(placement);
  const requestConfig = AdManager.getAdRequestConfig(placement);

  useEffect(() => {
    // If native ads are not available, skip rewarded setup
    if (!RewardedAd || !AdEventType || !RewardedAdEventType) {
      console.log('Rewarded ads not available, skipping setup');
      onAdFailedToLoad('Rewarded ads not supported');
      return;
    }

    if (autoLoad) {
      loadAd();
    }

    return () => {
      if (rewardedRef.current) {
        rewardedRef.current = null;
      }
    };
  }, [placement, autoLoad]);

  const loadAd = () => {
    if (!RewardedAd) {
      console.log('Rewarded ads not available');
      onAdFailedToLoad('Rewarded ads not supported');
      return;
    }

    try {
      const rewarded = RewardedAd.createForAdRequest(unitId, requestConfig);
      rewardedRef.current = rewarded;

      // Set up event listeners
      const unsubscribeLoaded = rewarded.addAdEventListener(
        AdEventType.LOADED,
        () => {
          console.log(`Rewarded ad loaded for ${placement}`);
          setIsLoaded(true);
          AdManager.resetErrors(placement, 'rewarded');
          onAdLoaded();
        }
      );

      const unsubscribeFailedToLoad = rewarded.addAdEventListener(
        AdEventType.ERROR,
        (error) => {
          console.error(`Rewarded ad failed to load for ${placement}:`, error);
          setIsLoaded(false);
          AdManager.recordError(placement, 'rewarded', error);
          onAdFailedToLoad(error);
        }
      );

      const unsubscribeOpened = rewarded.addAdEventListener(
        AdEventType.OPENED,
        () => {
          console.log(`Rewarded ad opened for ${placement}`);
          setIsShowing(true);
          AdManager.recordImpression(placement, 'rewarded');
        }
      );

      const unsubscribeClosed = rewarded.addAdEventListener(
        AdEventType.CLOSED,
        () => {
          console.log(`Rewarded ad closed for ${placement}`);
          setIsShowing(false);
          setIsLoaded(false);
          onAdDismissed();
          
          // Preload next ad
          if (autoLoad) {
            setTimeout(() => loadAd(), 1000);
          }
        }
      );

      const unsubscribeEarnedReward = rewarded.addAdEventListener(
        RewardedAdEventType.EARNED_REWARD,
        (reward) => {
          console.log(`User earned reward for ${placement}:`, reward);
          AdManager.recordClick(placement, 'rewarded');
          onUserEarnedReward(reward);
        }
      );

      // Load the ad
      rewarded.load();

      // Return cleanup function
      return () => {
        unsubscribeLoaded();
        unsubscribeFailedToLoad();
        unsubscribeOpened();
        unsubscribeClosed();
        unsubscribeEarnedReward();
      };
    } catch (error) {
      console.error(`Failed to create rewarded ad for ${placement}:`, error);
      AdManager.recordError(placement, 'rewarded', error);
      onAdFailedToLoad(error);
    }
  };

  const showAd = () => {
    if (!RewardedAd) {
      console.log('Rewarded ads not available');
      Alert.alert('Ads Not Available', 'Rewarded ads are not supported on this device.');
      return false;
    }

    if (isLoaded && !isShowing && rewardedRef.current) {
      try {
        rewardedRef.current.show();
        return true;
      } catch (error) {
        console.error(`Failed to show rewarded ad for ${placement}:`, error);
        AdManager.recordError(placement, 'rewarded', error);
        Alert.alert('Ad Error', 'Failed to show the rewarded ad. Please try again.');
        return false;
      }
    } else {
      console.log(`Rewarded ad not ready for ${placement}`);
      if (!isLoaded) {
        Alert.alert('Ad Not Ready', 'The rewarded ad is still loading. Please try again in a moment.');
      }
      return false;
    }
  };

  const getIsLoaded = () => {
    return isLoaded;
  };

  const getIsShowing = () => {
    return isShowing;
  };

  // Expose methods through ref
  React.useImperativeHandle(rewardedRef, () => ({
    showAd,
    loadAd,
    isLoaded: getIsLoaded,
    isShowing: getIsShowing,
  }));

  // This component doesn't render anything
  return null;
};

export default RewardedAdComponent;
