import React, { useState, useEffect, createContext, useContext } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, Alert, Animated } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator, DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { Ionicons } from '@expo/vector-icons';

// Safe imports with fallbacks
let useFonts, Poppins_400Regular, Poppins_500Medium, Poppins_600SemiBold, Poppins_700Bold;
let SplashScreen, LinearGradient;
let auth, onAuthStateChanged;
let initializeAdMob, AdManager, notificationService;

try {
  const fonts = require('@expo-google-fonts/poppins');
  useFonts = fonts.useFonts;
  Poppins_400Regular = fonts.Poppins_400Regular;
  Poppins_500Medium = fonts.Poppins_500Medium;
  Poppins_600SemiBold = fonts.Poppins_600SemiBold;
  Poppins_700Bold = fonts.Poppins_700Bold;
} catch (error) {
  console.warn('Font loading not available:', error);
  useFonts = () => [true, null]; // Mock function
  Poppins_400Regular = Poppins_500Medium = Poppins_600SemiBold = Poppins_700Bold = 'System';
}

try {
  SplashScreen = require('expo-splash-screen');
} catch (error) {
  console.warn('SplashScreen not available:', error);
  SplashScreen = { preventAutoHideAsync: () => Promise.resolve(), hideAsync: () => Promise.resolve() };
}

try {
  const gradient = require('expo-linear-gradient');
  LinearGradient = gradient.LinearGradient;
} catch (error) {
  console.warn('LinearGradient not available:', error);
  LinearGradient = View; // Fallback to regular View
}

try {
  const firebaseAuth = require('./firebaseConfig');
  auth = firebaseAuth.auth;
  const firebaseAuthFunctions = require('firebase/auth');
  onAuthStateChanged = firebaseAuthFunctions.onAuthStateChanged;
} catch (error) {
  console.warn('Firebase not available:', error);
  auth = { currentUser: null };
  onAuthStateChanged = () => () => {};
}

try {
  const adMobService = require('./services/AdMobService');
  initializeAdMob = adMobService.initializeAdMob;
} catch (error) {
  console.warn('AdMobService not available:', error);
  initializeAdMob = () => Promise.resolve({ fallback: true });
}

try {
  const adManagerModule = require('./src/services/AdManager');
  AdManager = adManagerModule.default;
} catch (error) {
  console.warn('AdManager not available:', error);
  AdManager = { initialize: () => Promise.resolve() };
}

try {
  notificationService = require('./services/NotificationService').default;
} catch (error) {
  console.warn('NotificationService not available:', error);
  notificationService = {
    initialize: () => Promise.resolve(false),
    cleanup: () => {}
  };
}

// Simple app context without dark mode
const AppContext = createContext({
  soundEnabled: true,
  fontSizeMultiplier: 1
});

// Export AppContext for use in other components
export { AppContext };

const AppProvider = ({ children }) => {
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState(1);

  return (
    <AppContext.Provider value={{
      soundEnabled,
      setSoundEnabled,
      fontSizeMultiplier,
      setFontSizeMultiplier
    }}>
      {children}
    </AppContext.Provider>
  );
};

// Safe screen imports with static imports
const createFallbackScreen = (screenName) => () => (
  <View style={styles.errorContainer}>
    <Text style={styles.errorText}>Screen not available: {screenName}</Text>
    <Text style={styles.errorSubtext}>Please check your installation</Text>
  </View>
);

// Static imports with fallbacks
let HomeScreen, QuizScreen, QuizLevels, QuizQuestions, SettingsScreen;
let LoginScreen, RegisterScreen, ForgotPasswordScreen, ChangePasswordScreen;
let AiChatScreen, StudyToolsScreen, FlashcardsScreen, FormulaSheetsScreen;
let PracticeTestsScreen, MockExamsScreen, AdaptiveLearningScreen, SpacedRepetitionScreen;
let FavoritesScreen, StudyPlaylistsScreen, NotesScreen, TimedQuiz;
let ChapterScreen, VideoChapterScreen, BookUnitsScreen, PdfViewerScreen;

try {
  HomeScreen = require('./screens/HomeScreen').default;
} catch (error) {
  console.warn('HomeScreen not available:', error);
  HomeScreen = createFallbackScreen('HomeScreen');
}

try {
  QuizScreen = require('./screens/QuizScreen').default;
} catch (error) {
  console.warn('QuizScreen not available:', error);
  QuizScreen = createFallbackScreen('QuizScreen');
}

try {
  QuizLevels = require('./screens/QuizLevels').default;
} catch (error) {
  console.warn('QuizLevels not available:', error);
  QuizLevels = createFallbackScreen('QuizLevels');
}

try {
  QuizQuestions = require('./screens/QuizQuestions').default;
} catch (error) {
  console.warn('QuizQuestions not available:', error);
  QuizQuestions = createFallbackScreen('QuizQuestions');
}

try {
  SettingsScreen = require('./screens/SettingsScreen').default;
} catch (error) {
  console.warn('SettingsScreen not available:', error);
  SettingsScreen = createFallbackScreen('SettingsScreen');
}

try {
  LoginScreen = require('./screens/LoginScreen').default;
} catch (error) {
  console.warn('LoginScreen not available:', error);
  LoginScreen = createFallbackScreen('LoginScreen');
}

try {
  RegisterScreen = require('./screens/RegisterScreen').default;
} catch (error) {
  console.warn('RegisterScreen not available:', error);
  RegisterScreen = createFallbackScreen('RegisterScreen');
}

try {
  ForgotPasswordScreen = require('./screens/ForgotPasswordScreen').default;
} catch (error) {
  console.warn('ForgotPasswordScreen not available:', error);
  ForgotPasswordScreen = createFallbackScreen('ForgotPasswordScreen');
}

try {
  ChangePasswordScreen = require('./screens/ChangePasswordScreen').default;
} catch (error) {
  console.warn('ChangePasswordScreen not available:', error);
  ChangePasswordScreen = createFallbackScreen('ChangePasswordScreen');
}

try {
  StudyToolsScreen = require('./screens/StudyToolsScreen').default;
} catch (error) {
  console.warn('StudyToolsScreen not available:', error);
  StudyToolsScreen = createFallbackScreen('StudyToolsScreen');
}

// Import screens with proper error handling
try {
  AiChatScreen = require('./screens/AiChatScreen').default;
} catch (error) {
  console.warn('AiChatScreen not available:', error);
  AiChatScreen = createFallbackScreen('AiChatScreen');
}

try {
  FlashcardsScreen = require('./screens/FlashcardsScreen').default;
} catch (error) {
  console.warn('FlashcardsScreen not available:', error);
  FlashcardsScreen = createFallbackScreen('FlashcardsScreen');
}

try {
  FormulaSheetsScreen = require('./screens/FormulaSheetsScreen').default;
} catch (error) {
  console.warn('FormulaSheetsScreen not available:', error);
  FormulaSheetsScreen = createFallbackScreen('FormulaSheetsScreen');
}

try {
  PracticeTestsScreen = require('./screens/PracticeTestsScreen').default;
} catch (error) {
  console.warn('PracticeTestsScreen not available:', error);
  PracticeTestsScreen = createFallbackScreen('PracticeTestsScreen');
}

try {
  MockExamsScreen = require('./screens/MockExamsScreen').default;
} catch (error) {
  console.warn('MockExamsScreen not available:', error);
  MockExamsScreen = createFallbackScreen('MockExamsScreen');
}

try {
  AdaptiveLearningScreen = require('./screens/AdaptiveLearningScreen').default;
} catch (error) {
  console.warn('AdaptiveLearningScreen not available:', error);
  AdaptiveLearningScreen = createFallbackScreen('AdaptiveLearningScreen');
}

try {
  SpacedRepetitionScreen = require('./screens/SpacedRepetitionScreen').default;
} catch (error) {
  console.warn('SpacedRepetitionScreen not available:', error);
  SpacedRepetitionScreen = createFallbackScreen('SpacedRepetitionScreen');
}

try {
  FavoritesScreen = require('./screens/FavoritesScreen').default;
} catch (error) {
  console.warn('FavoritesScreen not available:', error);
  FavoritesScreen = createFallbackScreen('FavoritesScreen');
}

try {
  StudyPlaylistsScreen = require('./screens/StudyPlaylistsScreen').default;
} catch (error) {
  console.warn('StudyPlaylistsScreen not available:', error);
  StudyPlaylistsScreen = createFallbackScreen('StudyPlaylistsScreen');
}

try {
  NotesScreen = require('./screens/NotesScreen').default;
} catch (error) {
  console.warn('NotesScreen not available:', error);
  NotesScreen = createFallbackScreen('NotesScreen');
}

try {
  TimedQuiz = require('./screens/TimedQuiz').default;
} catch (error) {
  console.warn('TimedQuiz not available:', error);
  TimedQuiz = createFallbackScreen('TimedQuiz');
}

try {
  ChapterScreen = require('./screens/ChapterScreen').default;
} catch (error) {
  console.warn('ChapterScreen not available:', error);
  ChapterScreen = createFallbackScreen('ChapterScreen');
}

try {
  VideoChapterScreen = require('./screens/VideoChapterScreen').default;
} catch (error) {
  console.warn('VideoChapterScreen not available:', error);
  VideoChapterScreen = createFallbackScreen('VideoChapterScreen');
}

try {
  BookUnitsScreen = require('./screens/BookUnitsScreen').default;
} catch (error) {
  console.warn('BookUnitsScreen not available:', error);
  BookUnitsScreen = createFallbackScreen('BookUnitsScreen');
}

try {
  PdfViewerScreen = require('./screens/PdfViewerScreen').default;
} catch (error) {
  console.warn('PdfViewerScreen not available:', error);
  PdfViewerScreen = createFallbackScreen('PdfViewerScreen');
}


const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
            Something went wrong
          </Text>
          <Text style={{ fontSize: 14, textAlign: 'center', marginBottom: 20 }}>
            Please restart the app. If the problem persists, contact support.
          </Text>
          <TouchableOpacity
            style={{ backgroundColor: '#007AFF', padding: 10, borderRadius: 5 }}
            onPress={() => this.setState({ hasError: false, error: null })}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

const CustomDrawerContent = (props) => {
  return (
    <DrawerContentScrollView {...props} style={[styles.drawerContainer, { backgroundColor: '#fff' }]}>
      <View style={styles.drawerHeader}>
        <Text style={[styles.userName, { color: '#000' }]}>BeeTech</Text>
        <Text style={[styles.userEmail, { color: '#555' }]}>Welcome to the App</Text>
      </View>
      <DrawerItemList {...props} />
    </DrawerContentScrollView>
  );
};

// Removed SettingsScreenUpdated component definition

const QuizStackNavigator = () => (
  <Stack.Navigator>
    <Stack.Screen name="QuizScreen" component={QuizScreen} options={{ title: 'Select Category' }} />
    <Stack.Screen name="QuizLevels" component={QuizLevels} options={{ title: 'Select Level' }} />
    <Stack.Screen name="QuizQuestions" component={QuizQuestions} options={{ title: 'Quiz Questions' }} />
  </Stack.Navigator>
);

const MainDrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: { backgroundColor: '#fff' },
        drawerLabelStyle: { color: '#000' },
        drawerActiveTintColor: '#000',
        drawerInactiveTintColor: '#000',
      }}
    >
      <Drawer.Screen name="Home" component={HomeScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="home" size={size} color={color} /> }} />
      <Drawer.Screen name="Quiz" component={QuizStackNavigator} options={{ drawerIcon: ({ size, color }) => <Ionicons name="game-controller-outline" size={size} color={color} /> }} />
      <Drawer.Screen name="Settings" component={SettingsScreen} options={{ drawerIcon: ({ size, color }) => <Ionicons name="settings" size={size} color={color} /> }} />
    </Drawer.Navigator>
  );
};

// Custom Loading Screen Component
const LoadingScreen = ({ fontsLoaded = false, authInitializing = false }) => {
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;
  const rotateAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous rotation for loading indicator
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getLoadingText = () => {
    if (authInitializing) return 'Checking authentication...';
    return 'Loading BeeTech...';
  };

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2', '#f093fb']}
      style={styles.loadingContainer}
    >
      <Animated.View style={[styles.loadingContent, { opacity: fadeAnim }]}>
        <Animated.Image
          source={require('./assets/images/logo.png')}
          style={[
            styles.loadingLogo,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        />
        <Animated.Text style={[
          styles.appName,
          {
            color: '#fff',
            fontFamily: fontsLoaded ? 'Poppins_700Bold' : 'System'
          }
        ]}>
          BeeTech
        </Animated.Text>
        <Animated.Text style={[
          styles.appTagline,
          {
            color: '#f0f0f0',
            fontFamily: fontsLoaded ? 'Poppins_400Regular' : 'System'
          }
        ]}>
          Enhance your knowledge with fun quizzes!
        </Animated.Text>

        <Animated.View style={[styles.loadingIndicatorContainer, { transform: [{ rotate: spin }] }]}>
          <ActivityIndicator size="large" color="#ffffff" />
        </Animated.View>

        <Animated.Text style={[
          styles.loadingText,
          {
            color: '#e0e0e0',
            fontFamily: fontsLoaded ? 'Poppins_400Regular' : 'System'
          }
        ]}>
          {getLoadingText()}
        </Animated.Text>
      </Animated.View>
    </LinearGradient>
  );
};

function RootNavigator() {
  const [authInitializing, setAuthInitializing] = useState(true);
  const [user, setUser] = useState(null);
  const [appReady, setAppReady] = useState(false);

  // Safe font loading with error handling
  let [fontsLoaded, fontError] = useFonts({
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
  });

  // App initialization effect with better error handling
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log("Starting app initialization...");

        // Initialize critical services in parallel with timeouts
        const initPromises = [
          // Initialize AdMob with timeout
          Promise.race([
            initializeAdMob(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('AdMob initialization timeout')), 10000)
            )
          ]).catch(error => ({ fallback: true, error: error.message })),

          // Initialize notifications with timeout
          Promise.race([
            notificationService.initialize(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Notification initialization timeout')), 5000)
            )
          ]).catch(error => ({ fallback: true, error: error.message }))
        ];

        // Wait for all initializations with a timeout
        const results = await Promise.race([
          Promise.allSettled(initPromises),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('App initialization timeout')), 15000)
          )
        ]);

        console.log('Initialization results:', results);

        // Set app ready even if some services failed
        setAppReady(true);
      } catch (error) {
        console.error("App initialization error:", error);
        // Continue with fallback values
        setAppReady(true);
      }
    };

    // Start initialization
    initializeApp();

    // Set a safety timeout
    const timeoutId = setTimeout(() => {
      console.warn("App initialization timeout - forcing app to start");
      setAppReady(true);
    }, 20000); // 20 second timeout

    return () => clearTimeout(timeoutId);
  }, [fontsLoaded, fontError]);

  // Handle user state changes (Firebase Auth) with error handling
  useEffect(() => {
    let subscriber = null;
    let authTimeout = null;

    const setupAuth = async () => {
      try {
        console.log("Setting up Firebase auth state listener...");

        // Set a timeout to prevent infinite loading
        authTimeout = setTimeout(() => {
          console.warn("Auth state check timeout - assuming no user");
          setAuthInitializing(false);
          setUser(null);
        }, 5000); // 5 second timeout

        subscriber = onAuthStateChanged(auth, (user) => {
          try {
            console.log("onAuthStateChanged fired. User:", user ? user.uid : null);

            // Clear the timeout since we got a response
            if (authTimeout) {
              clearTimeout(authTimeout);
              authTimeout = null;
            }

            setUser(user);

            // Add a small delay to prevent flicker and ensure smooth transition
            setTimeout(() => {
              console.log("Setting authInitializing to false.");
              setAuthInitializing(false);
            }, 300); // 300ms delay for smooth transition
          } catch (error) {
            console.error("Error in auth state change handler:", error);
            if (authTimeout) {
              clearTimeout(authTimeout);
              authTimeout = null;
            }
            setAuthInitializing(false);
          }
        });
      } catch (error) {
        console.error("Error setting up Firebase auth:", error);
        if (authTimeout) {
          clearTimeout(authTimeout);
          authTimeout = null;
        }
        setAuthInitializing(false);
      }
    };

    if (appReady) {
      setupAuth();
    }

    return () => {
      if (authTimeout) {
        clearTimeout(authTimeout);
      }
      if (subscriber) {
        console.log("Unsubscribing Firebase auth state listener.");
        subscriber();
      }
    };
  }, [appReady]);

  // Prevent splash screen from hiding until fonts are loaded and auth is checked
  useEffect(() => {
    async function prepare() {
      await SplashScreen.preventAutoHideAsync();
    }
    prepare();
  }, []);

  // Hide splash screen once app is ready (less dependent on fonts)
  useEffect(() => {
    if (appReady) {
      async function hideSplash() {
        try {
          await SplashScreen.hideAsync();
        } catch (error) {
          console.warn('Error hiding splash screen:', error);
        }
      }
      hideSplash();
    }
  }, [appReady]);

  // Show loading screen while app is initializing or checking auth state
  if (!appReady || authInitializing) {
    return <LoadingScreen fontsLoaded={fontsLoaded} authInitializing={authInitializing} />; // Show custom loading screen
  }

  // Log font loading error if it occurs but continue with app
  if (fontError) {
    console.warn("Font Loading Error (continuing with system fonts):", fontError);
    // Continue with system fonts - don't block the app
  }

 return (
   <NavigationContainer>
     <Stack.Navigator>
         {user ? (
           // User is signed in, show main app with drawer
          <>
            <Stack.Screen
              name="MainApp"
              component={MainDrawerNavigator}
              options={{ headerShown: false }}
            />
            {/* Keep other screens accessible from MainApp */}
            <Stack.Screen
              name="ChangePassword"
              component={ChangePasswordScreen}
              options={{ headerShown: false }} // Screen has its own header/back button
            />
            <Stack.Screen
              name="AiChat"
              component={AiChatScreen}
              options={{ title: 'Ask AI Doubt' }} // Set a title for the header
            />
            {/* Study Tools Screens */}
            <Stack.Screen
              name="StudyTools"
              component={StudyToolsScreen}
              options={{ title: 'Study Tools' }}
            />
            <Stack.Screen
              name="Flashcards"
              component={FlashcardsScreen}
              options={{ title: 'Flashcards' }}
            />
            <Stack.Screen
              name="FormulaSheets"
              component={FormulaSheetsScreen}
              options={{ title: 'Formula Sheets' }}
            />
            <Stack.Screen
              name="PracticeTests"
              component={PracticeTestsScreen}
              options={{ title: 'Practice Tests' }}
            />
            <Stack.Screen
              name="MockExams"
              component={MockExamsScreen}
              options={{ title: 'Mock Exams' }}
            />
            <Stack.Screen
              name="AdaptiveLearning"
              component={AdaptiveLearningScreen}
              options={{ title: 'AI Study Plan' }}
            />
            <Stack.Screen
              name="SpacedRepetition"
              component={SpacedRepetitionScreen}
              options={{ title: 'Spaced Repetition' }}
            />
            {/* Content Organization Screens */}
            <Stack.Screen
              name="Favorites"
              component={FavoritesScreen}
              options={{ title: 'Favorites' }}
            />
            <Stack.Screen
              name="StudyPlaylists"
              component={StudyPlaylistsScreen}
              options={{ title: 'Study Playlists' }}
            />
            <Stack.Screen
              name="Notes"
              component={NotesScreen}
              options={{ title: 'My Notes' }}
            />
            {/* Add TimedQuiz screen for mock exams and practice tests */}
            <Stack.Screen
              name="TimedQuiz"
              component={TimedQuiz}
              options={{ headerShown: false }} // TimedQuiz has its own header
            />
            {/* Add ChapterScreen to the stack */}
            <Stack.Screen
              name="ChapterScreen"
              component={ChapterScreen}
              options={({ route }) => ({ title: `${route.params.category} Chapters` })}
            />
            {/* Add VideoChapterScreen to the stack */}
            <Stack.Screen
              name="VideoChapterScreen"
              component={VideoChapterScreen}
              options={({ route }) => ({ title: `${route.params.category} Video Chapters` })} // Dynamic title
            />
            {/* Add BookUnitsScreen to the stack */}
            <Stack.Screen
              name="BookUnitsScreen"
              component={BookUnitsScreen}
              options={({ route }) => ({ title: route.params.bookTitle || 'Book Units' })} // Dynamic title based on book
            />
            {/* Add PdfViewerScreen to the stack */}
            <Stack.Screen
              name="PdfViewer"
              component={PdfViewerScreen}
              options={{ title: 'PDF Viewer' }} // You can customize the title
            />
          </>
         ) : (
          // No user is signed in, show auth screens
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{ headerShown: false }}
            />
          </>
        )}
         {/* Screens accessible regardless of auth state (if any needed) */}
        {/* Example: A public info screen */}
        {/* <Stack.Screen name="PublicInfo" component={PublicInfoScreen} /> */}
       </Stack.Navigator>
   </NavigationContainer>
 );
}


// Safe App wrapper with multiple layers of error protection
function SafeApp() {
  try {
    return (
      <ErrorBoundary>
        <AppProvider>
          <RootNavigator />
        </AppProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error("Critical app error:", error);
    // Fallback UI if everything fails
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
          App Error
        </Text>
        <Text style={{ fontSize: 14, textAlign: 'center' }}>
          Please restart the app
        </Text>
      </View>
    );
  }
}

export default function App() {
  return <SafeApp />;
}


const styles = StyleSheet.create({
  drawerContainer: { flex: 1 },
  drawerHeader: { padding: 20, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: '#ccc' },
  userName: { fontSize: 18, fontWeight: 'bold', marginBottom: 5 },
  userEmail: { fontSize: 14 },
  // Error container styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5'
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333'
  },
  errorSubtext: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666'
  },
  // Loading Screen Styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingLogo: {
    width: 120,
    height: 120,
    marginBottom: 20,
    borderRadius: 60,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  loadingIndicatorContainer: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 14,
  },
  // Removed themeToggleContainer and themeToggleText styles
  // Removed container and settingRow styles (they belonged to SettingsScreenUpdated)
});
