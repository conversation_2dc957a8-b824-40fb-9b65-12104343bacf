#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1314576 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=33372, tid=31200
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -XX:MaxMetaspaceSize=384m -XX:+HeapDumpOnOutOfMemoryError -Xms256m -Xmx512m -Dfile.encoding=utf8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9

Host: AMD Ryzen 7 4800H with Radeon Graphics         , 16 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Mon May 26 20:54:58 2025 India Standard Time elapsed time: 720.889235 seconds (0d 0h 12m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002beb4f597d0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=31200, stack(0x000000ce54a00000,0x000000ce54b00000) (1024K)]


Current CompileTask:
C2:720889 30639       4       groovyjarjarantlr4.v4.runtime.atn.ParserATNSimulator::computeTargetState (617 bytes)

Stack: [0x000000ce54a00000,0x000000ce54b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002beb4d9b250, length=51, elements={
0x000002be99640190, 0x000002beb4f2ef90, 0x000002beb4f31ba0, 0x000002beb4f328d0,
0x000002beb4f34a20, 0x000002beb4f55630, 0x000002beb4f56890, 0x000002beb4f597d0,
0x000002beb4f5a770, 0x000002beca277790, 0x000002beca346120, 0x000002beca3eeab0,
0x000002beca3efe60, 0x000002beca3ed700, 0x000002becfcf5b80, 0x000002becfcf54f0,
0x000002becfcf6210, 0x000002becfcf9d20, 0x000002becfcf68a0, 0x000002becfcfb0d0,
0x000002becfcfa3b0, 0x000002becfcfcb10, 0x000002becfcfc480, 0x000002bed12868d0,
0x000002bed1287c80, 0x000002bed12875f0, 0x000002bed12889a0, 0x000002bed1286f60,
0x000002bed1285520, 0x000002bed128be20, 0x000002bed128a3e0, 0x000002becfcf8970,
0x000002becfcfbdf0, 0x000002beca3ef7d0, 0x000002bed076a030, 0x000002bed076b3e0,
0x000002bed3360720, 0x000002bed45645a0, 0x000002bed29092d0, 0x000002bed2a98480,
0x000002bed335ece0, 0x000002bed335f370, 0x000002bed335fa00, 0x000002bed3360090,
0x000002bed3360db0, 0x000002bed4c3e3f0, 0x000002bed4c3ea80, 0x000002bed4c3fe30,
0x000002bed4c3f7a0, 0x000002bed4c3f110, 0x000002bed5773e70
}

Java Threads: ( => current thread )
  0x000002be99640190 JavaThread "main"                              [_thread_blocked, id=29496, stack(0x000000ce53c00000,0x000000ce53d00000) (1024K)]
  0x000002beb4f2ef90 JavaThread "Reference Handler"          daemon [_thread_blocked, id=29572, stack(0x000000ce54400000,0x000000ce54500000) (1024K)]
  0x000002beb4f31ba0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10708, stack(0x000000ce54500000,0x000000ce54600000) (1024K)]
  0x000002beb4f328d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23012, stack(0x000000ce54600000,0x000000ce54700000) (1024K)]
  0x000002beb4f34a20 JavaThread "Attach Listener"            daemon [_thread_blocked, id=2208, stack(0x000000ce54700000,0x000000ce54800000) (1024K)]
  0x000002beb4f55630 JavaThread "Service Thread"             daemon [_thread_blocked, id=32188, stack(0x000000ce54800000,0x000000ce54900000) (1024K)]
  0x000002beb4f56890 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=4216, stack(0x000000ce54900000,0x000000ce54a00000) (1024K)]
=>0x000002beb4f597d0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=31200, stack(0x000000ce54a00000,0x000000ce54b00000) (1024K)]
  0x000002beb4f5a770 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=4912, stack(0x000000ce54b00000,0x000000ce54c00000) (1024K)]
  0x000002beca277790 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=33484, stack(0x000000ce54d00000,0x000000ce54e00000) (1024K)]
  0x000002beca346120 JavaThread "Notification Thread"        daemon [_thread_blocked, id=23084, stack(0x000000ce54f00000,0x000000ce55000000) (1024K)]
  0x000002beca3eeab0 JavaThread "Daemon health stats"               [_thread_blocked, id=34672, stack(0x000000ce55500000,0x000000ce55600000) (1024K)]
  0x000002beca3efe60 JavaThread "Incoming local TCP Connector on port 54295"        [_thread_in_native, id=29312, stack(0x000000ce55600000,0x000000ce55700000) (1024K)]
  0x000002beca3ed700 JavaThread "Daemon periodic checks"            [_thread_blocked, id=34184, stack(0x000000ce55700000,0x000000ce55800000) (1024K)]
  0x000002becfcf5b80 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=32924, stack(0x000000ce55f00000,0x000000ce56000000) (1024K)]
  0x000002becfcf54f0 JavaThread "File lock request listener"        [_thread_in_native, id=30728, stack(0x000000ce56000000,0x000000ce56100000) (1024K)]
  0x000002becfcf6210 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.9\fileHashes)"        [_thread_blocked, id=34128, stack(0x000000ce56100000,0x000000ce56200000) (1024K)]
  0x000002becfcf9d20 JavaThread "File watcher server"        daemon [_thread_in_native, id=29776, stack(0x000000ce56c00000,0x000000ce56d00000) (1024K)]
  0x000002becfcf68a0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=34476, stack(0x000000ce56d00000,0x000000ce56e00000) (1024K)]
  0x000002becfcfb0d0 JavaThread "jar transforms"                    [_thread_blocked, id=31108, stack(0x000000ce56e00000,0x000000ce56f00000) (1024K)]
  0x000002becfcfa3b0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=5656, stack(0x000000ce56f00000,0x000000ce57000000) (1024K)]
  0x000002becfcfcb10 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=22832, stack(0x000000ce57000000,0x000000ce57100000) (1024K)]
  0x000002becfcfc480 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.9\fileContent)"        [_thread_blocked, id=20312, stack(0x000000ce57200000,0x000000ce57300000) (1024K)]
  0x000002bed12868d0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=33560, stack(0x000000ce57600000,0x000000ce57700000) (1024K)]
  0x000002bed1287c80 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=32400, stack(0x000000ce57700000,0x000000ce57800000) (1024K)]
  0x000002bed12875f0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=23868, stack(0x000000ce57800000,0x000000ce57900000) (1024K)]
  0x000002bed12889a0 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=28312, stack(0x000000ce57900000,0x000000ce57a00000) (1024K)]
  0x000002bed1286f60 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=32272, stack(0x000000ce57a00000,0x000000ce57b00000) (1024K)]
  0x000002bed1285520 JavaThread "jar transforms Thread 9"           [_thread_blocked, id=16136, stack(0x000000ce57b00000,0x000000ce57c00000) (1024K)]
  0x000002bed128be20 JavaThread "jar transforms Thread 10"          [_thread_blocked, id=22100, stack(0x000000ce57c00000,0x000000ce57d00000) (1024K)]
  0x000002bed128a3e0 JavaThread "jar transforms Thread 11"          [_thread_blocked, id=33796, stack(0x000000ce57d00000,0x000000ce57e00000) (1024K)]
  0x000002becfcf8970 JavaThread "jar transforms Thread 12"          [_thread_blocked, id=5872, stack(0x000000ce57e00000,0x000000ce57f00000) (1024K)]
  0x000002becfcfbdf0 JavaThread "jar transforms Thread 13"          [_thread_blocked, id=31452, stack(0x000000ce57f00000,0x000000ce58000000) (1024K)]
  0x000002beca3ef7d0 JavaThread "jar transforms Thread 14"          [_thread_blocked, id=15344, stack(0x000000ce58000000,0x000000ce58100000) (1024K)]
  0x000002bed076a030 JavaThread "jar transforms Thread 15"          [_thread_blocked, id=28876, stack(0x000000ce53a00000,0x000000ce53b00000) (1024K)]
  0x000002bed076b3e0 JavaThread "jar transforms Thread 16"          [_thread_blocked, id=13220, stack(0x000000ce57500000,0x000000ce57600000) (1024K)]
  0x000002bed3360720 JavaThread "Memory manager"                    [_thread_blocked, id=29872, stack(0x000000ce5c700000,0x000000ce5c800000) (1024K)]
  0x000002bed45645a0 JavaThread "Daemon Thread 3"                   [_thread_blocked, id=25424, stack(0x000000ce53900000,0x000000ce53a00000) (1024K)]
  0x000002bed29092d0 JavaThread "Daemon worker Thread 3"            [_thread_in_Java, id=22476, stack(0x000000ce55900000,0x000000ce55a00000) (1024K)]
  0x000002bed2a98480 JavaThread "VFS cleanup Thread 5"              [_thread_blocked, id=18724, stack(0x000000ce55a00000,0x000000ce55b00000) (1024K)]
  0x000002bed335ece0 JavaThread "Handler for socket connection from /127.0.0.1:54295 to /127.0.0.1:54704"        [_thread_in_native, id=4636, stack(0x000000ce53b00000,0x000000ce53c00000) (1024K)]
  0x000002bed335f370 JavaThread "Cancel handler"                    [_thread_blocked, id=29308, stack(0x000000ce55800000,0x000000ce55900000) (1024K)]
  0x000002bed335fa00 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:54295 to /127.0.0.1:54704"        [_thread_blocked, id=10784, stack(0x000000ce55b00000,0x000000ce55c00000) (1024K)]
  0x000002bed3360090 JavaThread "Stdin handler"                     [_thread_blocked, id=30328, stack(0x000000ce55c00000,0x000000ce55d00000) (1024K)]
  0x000002bed3360db0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=29772, stack(0x000000ce55d00000,0x000000ce55e00000) (1024K)]
  0x000002bed4c3e3f0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\.gradle\8.9\fileHashes)"        [_thread_blocked, id=19528, stack(0x000000ce55e00000,0x000000ce55f00000) (1024K)]
  0x000002bed4c3ea80 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=34532, stack(0x000000ce56200000,0x000000ce56300000) (1024K)]
  0x000002bed4c3fe30 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\quiz-bee-techs\node_modules\react-native-haptic-feedback\android\.gradle\8.9\checksums)"        [_thread_blocked, id=32552, stack(0x000000ce56a00000,0x000000ce56b00000) (1024K)]
  0x000002bed4c3f7a0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.9\md-rule)"        [_thread_blocked, id=34104, stack(0x000000ce56b00000,0x000000ce56c00000) (1024K)]
  0x000002bed4c3f110 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.9\md-supplier)"        [_thread_blocked, id=16272, stack(0x000000ce57100000,0x000000ce57200000) (1024K)]
  0x000002bed5773e70 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=15084, stack(0x000000ce57300000,0x000000ce57400000) (1024K)]
Total: 51

Other Threads:
  0x000002beb4f14cc0 VMThread "VM Thread"                           [id=16652, stack(0x000000ce54300000,0x000000ce54400000) (1024K)]
  0x000002beca0819e0 WatcherThread "VM Periodic Task Thread"        [id=28068, stack(0x000000ce54200000,0x000000ce54300000) (1024K)]
  0x000002be9966abd0 WorkerThread "GC Thread#0"                     [id=34160, stack(0x000000ce53d00000,0x000000ce53e00000) (1024K)]
  0x000002becaae4630 WorkerThread "GC Thread#1"                     [id=9952, stack(0x000000ce55000000,0x000000ce55100000) (1024K)]
  0x000002becaae49d0 WorkerThread "GC Thread#2"                     [id=22232, stack(0x000000ce55100000,0x000000ce55200000) (1024K)]
  0x000002becaae4d70 WorkerThread "GC Thread#3"                     [id=31428, stack(0x000000ce55200000,0x000000ce55300000) (1024K)]
  0x000002becaae5110 WorkerThread "GC Thread#4"                     [id=28784, stack(0x000000ce55300000,0x000000ce55400000) (1024K)]
  0x000002becaae54b0 WorkerThread "GC Thread#5"                     [id=32504, stack(0x000000ce55400000,0x000000ce55500000) (1024K)]
  0x000002bed1826870 WorkerThread "GC Thread#6"                     [id=13872, stack(0x000000ce54c00000,0x000000ce54d00000) (1024K)]
  0x000002bed18264d0 WorkerThread "GC Thread#7"                     [id=13840, stack(0x000000ce54e00000,0x000000ce54f00000) (1024K)]
  0x000002bed1826c10 WorkerThread "GC Thread#8"                     [id=15432, stack(0x000000ce56300000,0x000000ce56400000) (1024K)]
  0x000002bed18259f0 WorkerThread "GC Thread#9"                     [id=31972, stack(0x000000ce56400000,0x000000ce56500000) (1024K)]
  0x000002bed18252b0 WorkerThread "GC Thread#10"                    [id=1908, stack(0x000000ce56500000,0x000000ce56600000) (1024K)]
  0x000002bed1825650 WorkerThread "GC Thread#11"                    [id=33872, stack(0x000000ce56600000,0x000000ce56700000) (1024K)]
  0x000002bed1825d90 WorkerThread "GC Thread#12"                    [id=14660, stack(0x000000ce56700000,0x000000ce56800000) (1024K)]
  0x000002be99670970 ConcurrentGCThread "G1 Main Marker"            [id=24244, stack(0x000000ce53e00000,0x000000ce53f00000) (1024K)]
  0x000002be996727b0 WorkerThread "G1 Conc#0"                       [id=8936, stack(0x000000ce53f00000,0x000000ce54000000) (1024K)]
  0x000002bed1fbef10 WorkerThread "G1 Conc#1"                       [id=28076, stack(0x000000ce56800000,0x000000ce56900000) (1024K)]
  0x000002bed1fbeb70 WorkerThread "G1 Conc#2"                       [id=18020, stack(0x000000ce56900000,0x000000ce56a00000) (1024K)]
  0x000002beb4e4d310 ConcurrentGCThread "G1 Refine#0"               [id=29856, stack(0x000000ce54000000,0x000000ce54100000) (1024K)]
  0x000002beb4e4db30 ConcurrentGCThread "G1 Service"                [id=6676, stack(0x000000ce54100000,0x000000ce54200000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread0  721119 30639       4       groovyjarjarantlr4.v4.runtime.atn.ParserATNSimulator::computeTargetState (617 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000e0000000, size: 512 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002beb5000000-0x000002beb5c80000-0x000002beb5c80000), size 13107200, SharedBaseAddress: 0x000002beb5000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002beb6000000-0x000002beca000000, reserved size: 335544320
Narrow klass base: 0x000002beb5000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 16 total, 16 available
 Memory: 7599M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 256M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 512M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 336896K, used 249199K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 6 survivors (6144K)
 Metaspace       used 145883K, committed 147968K, reserved 458752K
  class space    used 18826K, committed 19840K, reserved 327680K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000e0000000, 0x00000000e0100000, 0x00000000e0100000|100%|HS|  |TAMS 0x00000000e0000000| PB 0x00000000e0000000| Complete 
|   1|0x00000000e0100000, 0x00000000e0200000, 0x00000000e0200000|100%|HC|  |TAMS 0x00000000e0100000| PB 0x00000000e0100000| Complete 
|   2|0x00000000e0200000, 0x00000000e0300000, 0x00000000e0300000|100%|HC|  |TAMS 0x00000000e0200000| PB 0x00000000e0200000| Complete 
|   3|0x00000000e0300000, 0x00000000e0400000, 0x00000000e0400000|100%|HC|  |TAMS 0x00000000e0300000| PB 0x00000000e0300000| Complete 
|   4|0x00000000e0400000, 0x00000000e0500000, 0x00000000e0500000|100%| O|  |TAMS 0x00000000e0400000| PB 0x00000000e0400000| Untracked 
|   5|0x00000000e0500000, 0x00000000e0600000, 0x00000000e0600000|100%| O|  |TAMS 0x00000000e0500000| PB 0x00000000e0500000| Untracked 
|   6|0x00000000e0600000, 0x00000000e0700000, 0x00000000e0700000|100%| O|  |TAMS 0x00000000e0600000| PB 0x00000000e0600000| Untracked 
|   7|0x00000000e0700000, 0x00000000e0800000, 0x00000000e0800000|100%| O|  |TAMS 0x00000000e0700000| PB 0x00000000e0700000| Untracked 
|   8|0x00000000e0800000, 0x00000000e0900000, 0x00000000e0900000|100%| O|  |TAMS 0x00000000e0800000| PB 0x00000000e0800000| Untracked 
|   9|0x00000000e0900000, 0x00000000e0a00000, 0x00000000e0a00000|100%| O|  |TAMS 0x00000000e0900000| PB 0x00000000e0900000| Untracked 
|  10|0x00000000e0a00000, 0x00000000e0b00000, 0x00000000e0b00000|100%|HS|  |TAMS 0x00000000e0a00000| PB 0x00000000e0a00000| Complete 
|  11|0x00000000e0b00000, 0x00000000e0c00000, 0x00000000e0c00000|100%| O|  |TAMS 0x00000000e0b00000| PB 0x00000000e0b00000| Untracked 
|  12|0x00000000e0c00000, 0x00000000e0d00000, 0x00000000e0d00000|100%| O|  |TAMS 0x00000000e0c00000| PB 0x00000000e0c00000| Untracked 
|  13|0x00000000e0d00000, 0x00000000e0e00000, 0x00000000e0e00000|100%| O|  |TAMS 0x00000000e0d00000| PB 0x00000000e0d00000| Untracked 
|  14|0x00000000e0e00000, 0x00000000e0f00000, 0x00000000e0f00000|100%| O|  |TAMS 0x00000000e0e00000| PB 0x00000000e0e00000| Untracked 
|  15|0x00000000e0f00000, 0x00000000e1000000, 0x00000000e1000000|100%| O|  |TAMS 0x00000000e0f00000| PB 0x00000000e0f00000| Untracked 
|  16|0x00000000e1000000, 0x00000000e1100000, 0x00000000e1100000|100%| O|  |TAMS 0x00000000e1000000| PB 0x00000000e1000000| Untracked 
|  17|0x00000000e1100000, 0x00000000e1200000, 0x00000000e1200000|100%| O|  |TAMS 0x00000000e1100000| PB 0x00000000e1100000| Untracked 
|  18|0x00000000e1200000, 0x00000000e1300000, 0x00000000e1300000|100%| O|  |TAMS 0x00000000e1200000| PB 0x00000000e1200000| Untracked 
|  19|0x00000000e1300000, 0x00000000e1400000, 0x00000000e1400000|100%| O|  |TAMS 0x00000000e1300000| PB 0x00000000e1300000| Untracked 
|  20|0x00000000e1400000, 0x00000000e1500000, 0x00000000e1500000|100%| O|  |TAMS 0x00000000e1400000| PB 0x00000000e1400000| Untracked 
|  21|0x00000000e1500000, 0x00000000e1600000, 0x00000000e1600000|100%| O|  |TAMS 0x00000000e1500000| PB 0x00000000e1500000| Untracked 
|  22|0x00000000e1600000, 0x00000000e1700000, 0x00000000e1700000|100%| O|  |TAMS 0x00000000e1600000| PB 0x00000000e1600000| Untracked 
|  23|0x00000000e1700000, 0x00000000e1800000, 0x00000000e1800000|100%| O|  |TAMS 0x00000000e1700000| PB 0x00000000e1700000| Untracked 
|  24|0x00000000e1800000, 0x00000000e1900000, 0x00000000e1900000|100%| O|  |TAMS 0x00000000e1800000| PB 0x00000000e1800000| Untracked 
|  25|0x00000000e1900000, 0x00000000e1a00000, 0x00000000e1a00000|100%|HS|  |TAMS 0x00000000e1900000| PB 0x00000000e1900000| Complete 
|  26|0x00000000e1a00000, 0x00000000e1b00000, 0x00000000e1b00000|100%|HC|  |TAMS 0x00000000e1a00000| PB 0x00000000e1a00000| Complete 
|  27|0x00000000e1b00000, 0x00000000e1c00000, 0x00000000e1c00000|100%|HS|  |TAMS 0x00000000e1b00000| PB 0x00000000e1b00000| Complete 
|  28|0x00000000e1c00000, 0x00000000e1d00000, 0x00000000e1d00000|100%|HC|  |TAMS 0x00000000e1c00000| PB 0x00000000e1c00000| Complete 
|  29|0x00000000e1d00000, 0x00000000e1e00000, 0x00000000e1e00000|100%|HC|  |TAMS 0x00000000e1d00000| PB 0x00000000e1d00000| Complete 
|  30|0x00000000e1e00000, 0x00000000e1f00000, 0x00000000e1f00000|100%|HC|  |TAMS 0x00000000e1e00000| PB 0x00000000e1e00000| Complete 
|  31|0x00000000e1f00000, 0x00000000e2000000, 0x00000000e2000000|100%| O|  |TAMS 0x00000000e1f00000| PB 0x00000000e1f00000| Untracked 
|  32|0x00000000e2000000, 0x00000000e2100000, 0x00000000e2100000|100%| O|  |TAMS 0x00000000e2000000| PB 0x00000000e2000000| Untracked 
|  33|0x00000000e2100000, 0x00000000e2200000, 0x00000000e2200000|100%| O|  |TAMS 0x00000000e2100000| PB 0x00000000e2100000| Untracked 
|  34|0x00000000e2200000, 0x00000000e2300000, 0x00000000e2300000|100%| O|  |TAMS 0x00000000e2200000| PB 0x00000000e2200000| Untracked 
|  35|0x00000000e2300000, 0x00000000e2400000, 0x00000000e2400000|100%| O|  |TAMS 0x00000000e2300000| PB 0x00000000e2300000| Untracked 
|  36|0x00000000e2400000, 0x00000000e2500000, 0x00000000e2500000|100%| O|  |TAMS 0x00000000e2400000| PB 0x00000000e2400000| Untracked 
|  37|0x00000000e2500000, 0x00000000e2600000, 0x00000000e2600000|100%| O|  |TAMS 0x00000000e2500000| PB 0x00000000e2500000| Untracked 
|  38|0x00000000e2600000, 0x00000000e2700000, 0x00000000e2700000|100%| O|  |TAMS 0x00000000e2600000| PB 0x00000000e2600000| Untracked 
|  39|0x00000000e2700000, 0x00000000e2800000, 0x00000000e2800000|100%| O|  |TAMS 0x00000000e2700000| PB 0x00000000e2700000| Untracked 
|  40|0x00000000e2800000, 0x00000000e2900000, 0x00000000e2900000|100%| O|  |TAMS 0x00000000e2800000| PB 0x00000000e2800000| Untracked 
|  41|0x00000000e2900000, 0x00000000e2a00000, 0x00000000e2a00000|100%| O|  |TAMS 0x00000000e2900000| PB 0x00000000e2900000| Untracked 
|  42|0x00000000e2a00000, 0x00000000e2b00000, 0x00000000e2b00000|100%| O|  |TAMS 0x00000000e2a00000| PB 0x00000000e2a00000| Untracked 
|  43|0x00000000e2b00000, 0x00000000e2c00000, 0x00000000e2c00000|100%| O|  |TAMS 0x00000000e2b00000| PB 0x00000000e2b00000| Untracked 
|  44|0x00000000e2c00000, 0x00000000e2d00000, 0x00000000e2d00000|100%| O|  |TAMS 0x00000000e2c00000| PB 0x00000000e2c00000| Untracked 
|  45|0x00000000e2d00000, 0x00000000e2e00000, 0x00000000e2e00000|100%| O|  |TAMS 0x00000000e2d00000| PB 0x00000000e2d00000| Untracked 
|  46|0x00000000e2e00000, 0x00000000e2f00000, 0x00000000e2f00000|100%| O|  |TAMS 0x00000000e2e00000| PB 0x00000000e2e00000| Untracked 
|  47|0x00000000e2f00000, 0x00000000e3000000, 0x00000000e3000000|100%| O|  |TAMS 0x00000000e2f00000| PB 0x00000000e2f00000| Untracked 
|  48|0x00000000e3000000, 0x00000000e3100000, 0x00000000e3100000|100%| O|  |TAMS 0x00000000e3000000| PB 0x00000000e3000000| Untracked 
|  49|0x00000000e3100000, 0x00000000e3200000, 0x00000000e3200000|100%| O|  |TAMS 0x00000000e3100000| PB 0x00000000e3100000| Untracked 
|  50|0x00000000e3200000, 0x00000000e3300000, 0x00000000e3300000|100%| O|  |TAMS 0x00000000e3200000| PB 0x00000000e3200000| Untracked 
|  51|0x00000000e3300000, 0x00000000e3400000, 0x00000000e3400000|100%| O|  |TAMS 0x00000000e3300000| PB 0x00000000e3300000| Untracked 
|  52|0x00000000e3400000, 0x00000000e3500000, 0x00000000e3500000|100%| O|  |TAMS 0x00000000e3400000| PB 0x00000000e3400000| Untracked 
|  53|0x00000000e3500000, 0x00000000e3600000, 0x00000000e3600000|100%| O|  |TAMS 0x00000000e3500000| PB 0x00000000e3500000| Untracked 
|  54|0x00000000e3600000, 0x00000000e3700000, 0x00000000e3700000|100%| O|  |TAMS 0x00000000e3600000| PB 0x00000000e3600000| Untracked 
|  55|0x00000000e3700000, 0x00000000e3800000, 0x00000000e3800000|100%| O|  |TAMS 0x00000000e3700000| PB 0x00000000e3700000| Untracked 
|  56|0x00000000e3800000, 0x00000000e3900000, 0x00000000e3900000|100%| O|  |TAMS 0x00000000e3800000| PB 0x00000000e3800000| Untracked 
|  57|0x00000000e3900000, 0x00000000e3a00000, 0x00000000e3a00000|100%| O|  |TAMS 0x00000000e3900000| PB 0x00000000e3900000| Untracked 
|  58|0x00000000e3a00000, 0x00000000e3b00000, 0x00000000e3b00000|100%| O|  |TAMS 0x00000000e3a00000| PB 0x00000000e3a00000| Untracked 
|  59|0x00000000e3b00000, 0x00000000e3c00000, 0x00000000e3c00000|100%| O|  |TAMS 0x00000000e3b00000| PB 0x00000000e3b00000| Untracked 
|  60|0x00000000e3c00000, 0x00000000e3d00000, 0x00000000e3d00000|100%| O|  |TAMS 0x00000000e3c00000| PB 0x00000000e3c00000| Untracked 
|  61|0x00000000e3d00000, 0x00000000e3e00000, 0x00000000e3e00000|100%| O|  |TAMS 0x00000000e3d00000| PB 0x00000000e3d00000| Untracked 
|  62|0x00000000e3e00000, 0x00000000e3f00000, 0x00000000e3f00000|100%| O|  |TAMS 0x00000000e3e00000| PB 0x00000000e3e00000| Untracked 
|  63|0x00000000e3f00000, 0x00000000e4000000, 0x00000000e4000000|100%| O|  |TAMS 0x00000000e3f00000| PB 0x00000000e3f00000| Untracked 
|  64|0x00000000e4000000, 0x00000000e4100000, 0x00000000e4100000|100%| O|  |TAMS 0x00000000e4000000| PB 0x00000000e4000000| Untracked 
|  65|0x00000000e4100000, 0x00000000e4200000, 0x00000000e4200000|100%| O|  |TAMS 0x00000000e4100000| PB 0x00000000e4100000| Untracked 
|  66|0x00000000e4200000, 0x00000000e4200000, 0x00000000e4300000|  0%| F|  |TAMS 0x00000000e4200000| PB 0x00000000e4200000| Untracked 
|  67|0x00000000e4300000, 0x00000000e4400000, 0x00000000e4400000|100%| O|Cm|TAMS 0x00000000e4300000| PB 0x00000000e4300000| Complete 
|  68|0x00000000e4400000, 0x00000000e4500000, 0x00000000e4500000|100%| O|  |TAMS 0x00000000e4400000| PB 0x00000000e4400000| Untracked 
|  69|0x00000000e4500000, 0x00000000e4600000, 0x00000000e4600000|100%| O|  |TAMS 0x00000000e4500000| PB 0x00000000e4500000| Untracked 
|  70|0x00000000e4600000, 0x00000000e4600000, 0x00000000e4700000|  0%| F|  |TAMS 0x00000000e4600000| PB 0x00000000e4600000| Untracked 
|  71|0x00000000e4700000, 0x00000000e4800000, 0x00000000e4800000|100%| O|  |TAMS 0x00000000e4700000| PB 0x00000000e4700000| Untracked 
|  72|0x00000000e4800000, 0x00000000e4900000, 0x00000000e4900000|100%| O|  |TAMS 0x00000000e4800000| PB 0x00000000e4800000| Untracked 
|  73|0x00000000e4900000, 0x00000000e4a00000, 0x00000000e4a00000|100%| O|  |TAMS 0x00000000e4900000| PB 0x00000000e4900000| Untracked 
|  74|0x00000000e4a00000, 0x00000000e4b00000, 0x00000000e4b00000|100%| O|  |TAMS 0x00000000e4a00000| PB 0x00000000e4a00000| Untracked 
|  75|0x00000000e4b00000, 0x00000000e4c00000, 0x00000000e4c00000|100%| O|  |TAMS 0x00000000e4b00000| PB 0x00000000e4b00000| Untracked 
|  76|0x00000000e4c00000, 0x00000000e4d00000, 0x00000000e4d00000|100%| O|  |TAMS 0x00000000e4c00000| PB 0x00000000e4c00000| Untracked 
|  77|0x00000000e4d00000, 0x00000000e4e00000, 0x00000000e4e00000|100%| O|  |TAMS 0x00000000e4d00000| PB 0x00000000e4d00000| Untracked 
|  78|0x00000000e4e00000, 0x00000000e4f00000, 0x00000000e4f00000|100%| O|  |TAMS 0x00000000e4e00000| PB 0x00000000e4e00000| Untracked 
|  79|0x00000000e4f00000, 0x00000000e5000000, 0x00000000e5000000|100%| O|  |TAMS 0x00000000e4f00000| PB 0x00000000e4f00000| Untracked 
|  80|0x00000000e5000000, 0x00000000e5100000, 0x00000000e5100000|100%| O|  |TAMS 0x00000000e5000000| PB 0x00000000e5000000| Untracked 
|  81|0x00000000e5100000, 0x00000000e5200000, 0x00000000e5200000|100%| O|  |TAMS 0x00000000e5100000| PB 0x00000000e5100000| Untracked 
|  82|0x00000000e5200000, 0x00000000e5300000, 0x00000000e5300000|100%| O|  |TAMS 0x00000000e5200000| PB 0x00000000e5200000| Untracked 
|  83|0x00000000e5300000, 0x00000000e5400000, 0x00000000e5400000|100%| O|  |TAMS 0x00000000e5300000| PB 0x00000000e5300000| Untracked 
|  84|0x00000000e5400000, 0x00000000e5500000, 0x00000000e5500000|100%| O|  |TAMS 0x00000000e5400000| PB 0x00000000e5400000| Untracked 
|  85|0x00000000e5500000, 0x00000000e5600000, 0x00000000e5600000|100%|HS|  |TAMS 0x00000000e5500000| PB 0x00000000e5500000| Complete 
|  86|0x00000000e5600000, 0x00000000e5700000, 0x00000000e5700000|100%|HC|  |TAMS 0x00000000e5600000| PB 0x00000000e5600000| Complete 
|  87|0x00000000e5700000, 0x00000000e5800000, 0x00000000e5800000|100%| O|  |TAMS 0x00000000e5700000| PB 0x00000000e5700000| Untracked 
|  88|0x00000000e5800000, 0x00000000e5900000, 0x00000000e5900000|100%| O|  |TAMS 0x00000000e5800000| PB 0x00000000e5800000| Untracked 
|  89|0x00000000e5900000, 0x00000000e5a00000, 0x00000000e5a00000|100%| O|  |TAMS 0x00000000e5900000| PB 0x00000000e5900000| Untracked 
|  90|0x00000000e5a00000, 0x00000000e5b00000, 0x00000000e5b00000|100%| O|  |TAMS 0x00000000e5a00000| PB 0x00000000e5a00000| Untracked 
|  91|0x00000000e5b00000, 0x00000000e5c00000, 0x00000000e5c00000|100%| O|  |TAMS 0x00000000e5b00000| PB 0x00000000e5b00000| Untracked 
|  92|0x00000000e5c00000, 0x00000000e5d00000, 0x00000000e5d00000|100%| O|  |TAMS 0x00000000e5c00000| PB 0x00000000e5c00000| Untracked 
|  93|0x00000000e5d00000, 0x00000000e5e00000, 0x00000000e5e00000|100%| O|  |TAMS 0x00000000e5d00000| PB 0x00000000e5d00000| Untracked 
|  94|0x00000000e5e00000, 0x00000000e5f00000, 0x00000000e5f00000|100%|HS|  |TAMS 0x00000000e5e00000| PB 0x00000000e5e00000| Complete 
|  95|0x00000000e5f00000, 0x00000000e6000000, 0x00000000e6000000|100%| O|  |TAMS 0x00000000e5f00000| PB 0x00000000e5f00000| Untracked 
|  96|0x00000000e6000000, 0x00000000e6100000, 0x00000000e6100000|100%| O|  |TAMS 0x00000000e6000000| PB 0x00000000e6000000| Untracked 
|  97|0x00000000e6100000, 0x00000000e6200000, 0x00000000e6200000|100%| O|Cm|TAMS 0x00000000e6100000| PB 0x00000000e6100000| Complete 
|  98|0x00000000e6200000, 0x00000000e6300000, 0x00000000e6300000|100%| O|  |TAMS 0x00000000e6200000| PB 0x00000000e6200000| Untracked 
|  99|0x00000000e6300000, 0x00000000e6400000, 0x00000000e6400000|100%| O|  |TAMS 0x00000000e6300000| PB 0x00000000e6300000| Untracked 
| 100|0x00000000e6400000, 0x00000000e6500000, 0x00000000e6500000|100%|HS|  |TAMS 0x00000000e6400000| PB 0x00000000e6400000| Complete 
| 101|0x00000000e6500000, 0x00000000e6600000, 0x00000000e6600000|100%| O|  |TAMS 0x00000000e6500000| PB 0x00000000e6500000| Untracked 
| 102|0x00000000e6600000, 0x00000000e6700000, 0x00000000e6700000|100%| O|  |TAMS 0x00000000e6600000| PB 0x00000000e6600000| Untracked 
| 103|0x00000000e6700000, 0x00000000e6800000, 0x00000000e6800000|100%| O|  |TAMS 0x00000000e6700000| PB 0x00000000e6700000| Untracked 
| 104|0x00000000e6800000, 0x00000000e6900000, 0x00000000e6900000|100%| O|  |TAMS 0x00000000e6800000| PB 0x00000000e6800000| Untracked 
| 105|0x00000000e6900000, 0x00000000e6a00000, 0x00000000e6a00000|100%| O|  |TAMS 0x00000000e6900000| PB 0x00000000e6900000| Untracked 
| 106|0x00000000e6a00000, 0x00000000e6b00000, 0x00000000e6b00000|100%| O|  |TAMS 0x00000000e6a00000| PB 0x00000000e6a00000| Untracked 
| 107|0x00000000e6b00000, 0x00000000e6c00000, 0x00000000e6c00000|100%| O|  |TAMS 0x00000000e6b00000| PB 0x00000000e6b00000| Untracked 
| 108|0x00000000e6c00000, 0x00000000e6d00000, 0x00000000e6d00000|100%| O|  |TAMS 0x00000000e6c00000| PB 0x00000000e6c00000| Untracked 
| 109|0x00000000e6d00000, 0x00000000e6e00000, 0x00000000e6e00000|100%| O|  |TAMS 0x00000000e6d00000| PB 0x00000000e6d00000| Untracked 
| 110|0x00000000e6e00000, 0x00000000e6f00000, 0x00000000e6f00000|100%| O|  |TAMS 0x00000000e6e00000| PB 0x00000000e6e00000| Untracked 
| 111|0x00000000e6f00000, 0x00000000e6f6ba90, 0x00000000e7000000| 42%| O|  |TAMS 0x00000000e6f00000| PB 0x00000000e6f00000| Untracked 
| 112|0x00000000e7000000, 0x00000000e7100000, 0x00000000e7100000|100%|HS|  |TAMS 0x00000000e7000000| PB 0x00000000e7000000| Complete 
| 113|0x00000000e7100000, 0x00000000e7200000, 0x00000000e7200000|100%|HC|  |TAMS 0x00000000e7100000| PB 0x00000000e7100000| Complete 
| 114|0x00000000e7200000, 0x00000000e7300000, 0x00000000e7300000|100%| O|  |TAMS 0x00000000e7200000| PB 0x00000000e7200000| Untracked 
| 115|0x00000000e7300000, 0x00000000e7400000, 0x00000000e7400000|100%| O|  |TAMS 0x00000000e7300000| PB 0x00000000e7300000| Untracked 
| 116|0x00000000e7400000, 0x00000000e7500000, 0x00000000e7500000|100%|HS|  |TAMS 0x00000000e7400000| PB 0x00000000e7400000| Complete 
| 117|0x00000000e7500000, 0x00000000e7600000, 0x00000000e7600000|100%|HS|  |TAMS 0x00000000e7500000| PB 0x00000000e7500000| Complete 
| 118|0x00000000e7600000, 0x00000000e7700000, 0x00000000e7700000|100%|HC|  |TAMS 0x00000000e7600000| PB 0x00000000e7600000| Complete 
| 119|0x00000000e7700000, 0x00000000e7800000, 0x00000000e7800000|100%| O|  |TAMS 0x00000000e7700000| PB 0x00000000e7700000| Untracked 
| 120|0x00000000e7800000, 0x00000000e7900000, 0x00000000e7900000|100%|HS|  |TAMS 0x00000000e7800000| PB 0x00000000e7800000| Complete 
| 121|0x00000000e7900000, 0x00000000e7a00000, 0x00000000e7a00000|100%|HC|  |TAMS 0x00000000e7900000| PB 0x00000000e7900000| Complete 
| 122|0x00000000e7a00000, 0x00000000e7b00000, 0x00000000e7b00000|100%| O|  |TAMS 0x00000000e7a00000| PB 0x00000000e7a00000| Untracked 
| 123|0x00000000e7b00000, 0x00000000e7c00000, 0x00000000e7c00000|100%| O|  |TAMS 0x00000000e7b00000| PB 0x00000000e7b00000| Untracked 
| 124|0x00000000e7c00000, 0x00000000e7d00000, 0x00000000e7d00000|100%| O|  |TAMS 0x00000000e7c00000| PB 0x00000000e7c00000| Untracked 
| 125|0x00000000e7d00000, 0x00000000e7e00000, 0x00000000e7e00000|100%| O|  |TAMS 0x00000000e7d00000| PB 0x00000000e7d00000| Untracked 
| 126|0x00000000e7e00000, 0x00000000e7f00000, 0x00000000e7f00000|100%| O|  |TAMS 0x00000000e7e00000| PB 0x00000000e7e00000| Untracked 
| 127|0x00000000e7f00000, 0x00000000e8000000, 0x00000000e8000000|100%| O|  |TAMS 0x00000000e7f00000| PB 0x00000000e7f00000| Untracked 
| 128|0x00000000e8000000, 0x00000000e8100000, 0x00000000e8100000|100%| O|  |TAMS 0x00000000e8000000| PB 0x00000000e8000000| Untracked 
| 129|0x00000000e8100000, 0x00000000e8200000, 0x00000000e8200000|100%| O|Cm|TAMS 0x00000000e8100000| PB 0x00000000e8100000| Complete 
| 130|0x00000000e8200000, 0x00000000e8200000, 0x00000000e8300000|  0%| F|  |TAMS 0x00000000e8200000| PB 0x00000000e8200000| Untracked 
| 131|0x00000000e8300000, 0x00000000e8400000, 0x00000000e8400000|100%| O|  |TAMS 0x00000000e8300000| PB 0x00000000e8300000| Untracked 
| 132|0x00000000e8400000, 0x00000000e8500000, 0x00000000e8500000|100%| O|  |TAMS 0x00000000e8400000| PB 0x00000000e8400000| Untracked 
| 133|0x00000000e8500000, 0x00000000e8500000, 0x00000000e8600000|  0%| F|  |TAMS 0x00000000e8500000| PB 0x00000000e8500000| Untracked 
| 134|0x00000000e8600000, 0x00000000e8700000, 0x00000000e8700000|100%| O|  |TAMS 0x00000000e8600000| PB 0x00000000e8600000| Untracked 
| 135|0x00000000e8700000, 0x00000000e8800000, 0x00000000e8800000|100%| O|  |TAMS 0x00000000e8700000| PB 0x00000000e8700000| Untracked 
| 136|0x00000000e8800000, 0x00000000e8900000, 0x00000000e8900000|100%|HS|  |TAMS 0x00000000e8800000| PB 0x00000000e8800000| Complete 
| 137|0x00000000e8900000, 0x00000000e8a00000, 0x00000000e8a00000|100%| O|  |TAMS 0x00000000e8900000| PB 0x00000000e8900000| Untracked 
| 138|0x00000000e8a00000, 0x00000000e8b00000, 0x00000000e8b00000|100%| O|  |TAMS 0x00000000e8a00000| PB 0x00000000e8a00000| Untracked 
| 139|0x00000000e8b00000, 0x00000000e8c00000, 0x00000000e8c00000|100%| O|  |TAMS 0x00000000e8b00000| PB 0x00000000e8b00000| Untracked 
| 140|0x00000000e8c00000, 0x00000000e8d00000, 0x00000000e8d00000|100%|HS|  |TAMS 0x00000000e8c00000| PB 0x00000000e8c00000| Complete 
| 141|0x00000000e8d00000, 0x00000000e8e00000, 0x00000000e8e00000|100%|HC|  |TAMS 0x00000000e8d00000| PB 0x00000000e8d00000| Complete 
| 142|0x00000000e8e00000, 0x00000000e8f00000, 0x00000000e8f00000|100%|HC|  |TAMS 0x00000000e8e00000| PB 0x00000000e8e00000| Complete 
| 143|0x00000000e8f00000, 0x00000000e9000000, 0x00000000e9000000|100%|HC|  |TAMS 0x00000000e8f00000| PB 0x00000000e8f00000| Complete 
| 144|0x00000000e9000000, 0x00000000e9100000, 0x00000000e9100000|100%| O|  |TAMS 0x00000000e9000000| PB 0x00000000e9000000| Untracked 
| 145|0x00000000e9100000, 0x00000000e9100000, 0x00000000e9200000|  0%| F|  |TAMS 0x00000000e9100000| PB 0x00000000e9100000| Untracked 
| 146|0x00000000e9200000, 0x00000000e9300000, 0x00000000e9300000|100%| O|  |TAMS 0x00000000e9200000| PB 0x00000000e9200000| Untracked 
| 147|0x00000000e9300000, 0x00000000e9400000, 0x00000000e9400000|100%|HS|  |TAMS 0x00000000e9300000| PB 0x00000000e9300000| Complete 
| 148|0x00000000e9400000, 0x00000000e9500000, 0x00000000e9500000|100%|HC|  |TAMS 0x00000000e9400000| PB 0x00000000e9400000| Complete 
| 149|0x00000000e9500000, 0x00000000e9600000, 0x00000000e9600000|100%| O|  |TAMS 0x00000000e9500000| PB 0x00000000e9500000| Untracked 
| 150|0x00000000e9600000, 0x00000000e9700000, 0x00000000e9700000|100%|HS|  |TAMS 0x00000000e9600000| PB 0x00000000e9600000| Complete 
| 151|0x00000000e9700000, 0x00000000e9800000, 0x00000000e9800000|100%|HC|  |TAMS 0x00000000e9700000| PB 0x00000000e9700000| Complete 
| 152|0x00000000e9800000, 0x00000000e9800000, 0x00000000e9900000|  0%| F|  |TAMS 0x00000000e9800000| PB 0x00000000e9800000| Untracked 
| 153|0x00000000e9900000, 0x00000000e9a00000, 0x00000000e9a00000|100%| O|  |TAMS 0x00000000e9900000| PB 0x00000000e9900000| Untracked 
| 154|0x00000000e9a00000, 0x00000000e9b00000, 0x00000000e9b00000|100%| O|  |TAMS 0x00000000e9a00000| PB 0x00000000e9a00000| Untracked 
| 155|0x00000000e9b00000, 0x00000000e9c00000, 0x00000000e9c00000|100%| O|  |TAMS 0x00000000e9b00000| PB 0x00000000e9b00000| Untracked 
| 156|0x00000000e9c00000, 0x00000000e9d00000, 0x00000000e9d00000|100%| O|  |TAMS 0x00000000e9c00000| PB 0x00000000e9c00000| Untracked 
| 157|0x00000000e9d00000, 0x00000000e9e00000, 0x00000000e9e00000|100%| O|  |TAMS 0x00000000e9d00000| PB 0x00000000e9d00000| Untracked 
| 158|0x00000000e9e00000, 0x00000000e9f00000, 0x00000000e9f00000|100%| O|  |TAMS 0x00000000e9e00000| PB 0x00000000e9e00000| Untracked 
| 159|0x00000000e9f00000, 0x00000000ea000000, 0x00000000ea000000|100%| O|  |TAMS 0x00000000e9f00000| PB 0x00000000e9f00000| Untracked 
| 160|0x00000000ea000000, 0x00000000ea100000, 0x00000000ea100000|100%| O|  |TAMS 0x00000000ea000000| PB 0x00000000ea000000| Untracked 
| 161|0x00000000ea100000, 0x00000000ea200000, 0x00000000ea200000|100%| O|  |TAMS 0x00000000ea100000| PB 0x00000000ea100000| Untracked 
| 162|0x00000000ea200000, 0x00000000ea300000, 0x00000000ea300000|100%| O|  |TAMS 0x00000000ea200000| PB 0x00000000ea200000| Untracked 
| 163|0x00000000ea300000, 0x00000000ea400000, 0x00000000ea400000|100%| O|  |TAMS 0x00000000ea300000| PB 0x00000000ea300000| Untracked 
| 164|0x00000000ea400000, 0x00000000ea500000, 0x00000000ea500000|100%| O|  |TAMS 0x00000000ea400000| PB 0x00000000ea400000| Untracked 
| 165|0x00000000ea500000, 0x00000000ea600000, 0x00000000ea600000|100%| O|  |TAMS 0x00000000ea500000| PB 0x00000000ea500000| Untracked 
| 166|0x00000000ea600000, 0x00000000ea700000, 0x00000000ea700000|100%| O|  |TAMS 0x00000000ea600000| PB 0x00000000ea600000| Untracked 
| 167|0x00000000ea700000, 0x00000000ea800000, 0x00000000ea800000|100%| O|Cm|TAMS 0x00000000ea700000| PB 0x00000000ea700000| Complete 
| 168|0x00000000ea800000, 0x00000000ea900000, 0x00000000ea900000|100%| O|  |TAMS 0x00000000ea800000| PB 0x00000000ea800000| Untracked 
| 169|0x00000000ea900000, 0x00000000eaa00000, 0x00000000eaa00000|100%| O|  |TAMS 0x00000000ea900000| PB 0x00000000ea900000| Untracked 
| 170|0x00000000eaa00000, 0x00000000eab00000, 0x00000000eab00000|100%| O|Cm|TAMS 0x00000000eaa00000| PB 0x00000000eaa00000| Complete 
| 171|0x00000000eab00000, 0x00000000eac00000, 0x00000000eac00000|100%| O|  |TAMS 0x00000000eab00000| PB 0x00000000eab00000| Untracked 
| 172|0x00000000eac00000, 0x00000000ead00000, 0x00000000ead00000|100%| O|Cm|TAMS 0x00000000eac00000| PB 0x00000000eac00000| Complete 
| 173|0x00000000ead00000, 0x00000000eae00000, 0x00000000eae00000|100%| O|  |TAMS 0x00000000ead00000| PB 0x00000000ead00000| Untracked 
| 174|0x00000000eae00000, 0x00000000eaf00000, 0x00000000eaf00000|100%| O|  |TAMS 0x00000000eae00000| PB 0x00000000eae00000| Untracked 
| 175|0x00000000eaf00000, 0x00000000eb000000, 0x00000000eb000000|100%| O|  |TAMS 0x00000000eaf00000| PB 0x00000000eaf00000| Untracked 
| 176|0x00000000eb000000, 0x00000000eb100000, 0x00000000eb100000|100%| O|  |TAMS 0x00000000eb000000| PB 0x00000000eb000000| Untracked 
| 177|0x00000000eb100000, 0x00000000eb200000, 0x00000000eb200000|100%| O|  |TAMS 0x00000000eb100000| PB 0x00000000eb100000| Untracked 
| 178|0x00000000eb200000, 0x00000000eb200000, 0x00000000eb300000|  0%| F|  |TAMS 0x00000000eb200000| PB 0x00000000eb200000| Untracked 
| 179|0x00000000eb300000, 0x00000000eb400000, 0x00000000eb400000|100%| O|  |TAMS 0x00000000eb300000| PB 0x00000000eb300000| Untracked 
| 180|0x00000000eb400000, 0x00000000eb500000, 0x00000000eb500000|100%|HS|  |TAMS 0x00000000eb400000| PB 0x00000000eb400000| Complete 
| 181|0x00000000eb500000, 0x00000000eb600000, 0x00000000eb600000|100%|HC|  |TAMS 0x00000000eb500000| PB 0x00000000eb500000| Complete 
| 182|0x00000000eb600000, 0x00000000eb700000, 0x00000000eb700000|100%|HC|  |TAMS 0x00000000eb600000| PB 0x00000000eb600000| Complete 
| 183|0x00000000eb700000, 0x00000000eb800000, 0x00000000eb800000|100%|HC|  |TAMS 0x00000000eb700000| PB 0x00000000eb700000| Complete 
| 184|0x00000000eb800000, 0x00000000eb900000, 0x00000000eb900000|100%| O|  |TAMS 0x00000000eb800000| PB 0x00000000eb800000| Untracked 
| 185|0x00000000eb900000, 0x00000000eba00000, 0x00000000eba00000|100%| O|  |TAMS 0x00000000eb900000| PB 0x00000000eb900000| Untracked 
| 186|0x00000000eba00000, 0x00000000ebb00000, 0x00000000ebb00000|100%| O|  |TAMS 0x00000000eba00000| PB 0x00000000eba00000| Untracked 
| 187|0x00000000ebb00000, 0x00000000ebc00000, 0x00000000ebc00000|100%| O|  |TAMS 0x00000000ebb00000| PB 0x00000000ebb00000| Untracked 
| 188|0x00000000ebc00000, 0x00000000ebd00000, 0x00000000ebd00000|100%| O|  |TAMS 0x00000000ebc00000| PB 0x00000000ebc00000| Untracked 
| 189|0x00000000ebd00000, 0x00000000ebe00000, 0x00000000ebe00000|100%|HS|  |TAMS 0x00000000ebd00000| PB 0x00000000ebd00000| Complete 
| 190|0x00000000ebe00000, 0x00000000ebf00000, 0x00000000ebf00000|100%|HC|  |TAMS 0x00000000ebe00000| PB 0x00000000ebe00000| Complete 
| 191|0x00000000ebf00000, 0x00000000ec000000, 0x00000000ec000000|100%|HC|  |TAMS 0x00000000ebf00000| PB 0x00000000ebf00000| Complete 
| 192|0x00000000ec000000, 0x00000000ec100000, 0x00000000ec100000|100%|HC|  |TAMS 0x00000000ec000000| PB 0x00000000ec000000| Complete 
| 193|0x00000000ec100000, 0x00000000ec200000, 0x00000000ec200000|100%|HS|  |TAMS 0x00000000ec100000| PB 0x00000000ec100000| Complete 
| 194|0x00000000ec200000, 0x00000000ec300000, 0x00000000ec300000|100%|HC|  |TAMS 0x00000000ec200000| PB 0x00000000ec200000| Complete 
| 195|0x00000000ec300000, 0x00000000ec400000, 0x00000000ec400000|100%|HC|  |TAMS 0x00000000ec300000| PB 0x00000000ec300000| Complete 
| 196|0x00000000ec400000, 0x00000000ec500000, 0x00000000ec500000|100%|HC|  |TAMS 0x00000000ec400000| PB 0x00000000ec400000| Complete 
| 197|0x00000000ec500000, 0x00000000ec600000, 0x00000000ec600000|100%| O|  |TAMS 0x00000000ec500000| PB 0x00000000ec500000| Untracked 
| 198|0x00000000ec600000, 0x00000000ec700000, 0x00000000ec700000|100%| O|  |TAMS 0x00000000ec600000| PB 0x00000000ec600000| Untracked 
| 199|0x00000000ec700000, 0x00000000ec800000, 0x00000000ec800000|100%| O|  |TAMS 0x00000000ec700000| PB 0x00000000ec700000| Untracked 
| 200|0x00000000ec800000, 0x00000000ec900000, 0x00000000ec900000|100%| O|  |TAMS 0x00000000ec800000| PB 0x00000000ec800000| Untracked 
| 201|0x00000000ec900000, 0x00000000eca00000, 0x00000000eca00000|100%| O|  |TAMS 0x00000000ec900000| PB 0x00000000ec900000| Untracked 
| 202|0x00000000eca00000, 0x00000000ecb00000, 0x00000000ecb00000|100%| O|  |TAMS 0x00000000eca00000| PB 0x00000000eca00000| Untracked 
| 203|0x00000000ecb00000, 0x00000000ecc00000, 0x00000000ecc00000|100%| O|  |TAMS 0x00000000ecb00000| PB 0x00000000ecb00000| Untracked 
| 204|0x00000000ecc00000, 0x00000000ecd00000, 0x00000000ecd00000|100%| O|  |TAMS 0x00000000ecc00000| PB 0x00000000ecc00000| Untracked 
| 205|0x00000000ecd00000, 0x00000000ece00000, 0x00000000ece00000|100%| O|  |TAMS 0x00000000ecd00000| PB 0x00000000ecd00000| Untracked 
| 206|0x00000000ece00000, 0x00000000ece00000, 0x00000000ecf00000|  0%| F|  |TAMS 0x00000000ece00000| PB 0x00000000ece00000| Untracked 
| 207|0x00000000ecf00000, 0x00000000ecf00000, 0x00000000ed000000|  0%| F|  |TAMS 0x00000000ecf00000| PB 0x00000000ecf00000| Untracked 
| 208|0x00000000ed000000, 0x00000000ed000000, 0x00000000ed100000|  0%| F|  |TAMS 0x00000000ed000000| PB 0x00000000ed000000| Untracked 
| 209|0x00000000ed100000, 0x00000000ed100000, 0x00000000ed200000|  0%| F|  |TAMS 0x00000000ed100000| PB 0x00000000ed100000| Untracked 
| 210|0x00000000ed200000, 0x00000000ed200000, 0x00000000ed300000|  0%| F|  |TAMS 0x00000000ed200000| PB 0x00000000ed200000| Untracked 
| 211|0x00000000ed300000, 0x00000000ed400000, 0x00000000ed400000|100%| O|  |TAMS 0x00000000ed300000| PB 0x00000000ed300000| Untracked 
| 212|0x00000000ed400000, 0x00000000ed500000, 0x00000000ed500000|100%| O|  |TAMS 0x00000000ed400000| PB 0x00000000ed400000| Untracked 
| 213|0x00000000ed500000, 0x00000000ed600000, 0x00000000ed600000|100%| O|  |TAMS 0x00000000ed500000| PB 0x00000000ed500000| Untracked 
| 214|0x00000000ed600000, 0x00000000ed600000, 0x00000000ed700000|  0%| F|  |TAMS 0x00000000ed600000| PB 0x00000000ed600000| Untracked 
| 215|0x00000000ed700000, 0x00000000ed700000, 0x00000000ed800000|  0%| F|  |TAMS 0x00000000ed700000| PB 0x00000000ed700000| Untracked 
| 216|0x00000000ed800000, 0x00000000ed800000, 0x00000000ed900000|  0%| F|  |TAMS 0x00000000ed800000| PB 0x00000000ed800000| Untracked 
| 217|0x00000000ed900000, 0x00000000ed900000, 0x00000000eda00000|  0%| F|  |TAMS 0x00000000ed900000| PB 0x00000000ed900000| Untracked 
| 218|0x00000000eda00000, 0x00000000eda00000, 0x00000000edb00000|  0%| F|  |TAMS 0x00000000eda00000| PB 0x00000000eda00000| Untracked 
| 219|0x00000000edb00000, 0x00000000edb00000, 0x00000000edc00000|  0%| F|  |TAMS 0x00000000edb00000| PB 0x00000000edb00000| Untracked 
| 220|0x00000000edc00000, 0x00000000edcf0280, 0x00000000edd00000| 93%| S|CS|TAMS 0x00000000edc00000| PB 0x00000000edc00000| Complete 
| 221|0x00000000edd00000, 0x00000000ede00000, 0x00000000ede00000|100%| S|CS|TAMS 0x00000000edd00000| PB 0x00000000edd00000| Complete 
| 222|0x00000000ede00000, 0x00000000edf00000, 0x00000000edf00000|100%| S|CS|TAMS 0x00000000ede00000| PB 0x00000000ede00000| Complete 
| 223|0x00000000edf00000, 0x00000000ee000000, 0x00000000ee000000|100%| S|CS|TAMS 0x00000000edf00000| PB 0x00000000edf00000| Complete 
| 224|0x00000000ee000000, 0x00000000ee100000, 0x00000000ee100000|100%| S|CS|TAMS 0x00000000ee000000| PB 0x00000000ee000000| Complete 
| 225|0x00000000ee100000, 0x00000000ee200000, 0x00000000ee200000|100%| S|CS|TAMS 0x00000000ee100000| PB 0x00000000ee100000| Complete 
| 226|0x00000000ee200000, 0x00000000ee200000, 0x00000000ee300000|  0%| F|  |TAMS 0x00000000ee200000| PB 0x00000000ee200000| Untracked 
| 227|0x00000000ee300000, 0x00000000ee300000, 0x00000000ee400000|  0%| F|  |TAMS 0x00000000ee300000| PB 0x00000000ee300000| Untracked 
| 228|0x00000000ee400000, 0x00000000ee400000, 0x00000000ee500000|  0%| F|  |TAMS 0x00000000ee400000| PB 0x00000000ee400000| Untracked 
| 229|0x00000000ee500000, 0x00000000ee500000, 0x00000000ee600000|  0%| F|  |TAMS 0x00000000ee500000| PB 0x00000000ee500000| Untracked 
| 230|0x00000000ee600000, 0x00000000ee600000, 0x00000000ee700000|  0%| F|  |TAMS 0x00000000ee600000| PB 0x00000000ee600000| Untracked 
| 231|0x00000000ee700000, 0x00000000ee700000, 0x00000000ee800000|  0%| F|  |TAMS 0x00000000ee700000| PB 0x00000000ee700000| Untracked 
| 232|0x00000000ee800000, 0x00000000ee800000, 0x00000000ee900000|  0%| F|  |TAMS 0x00000000ee800000| PB 0x00000000ee800000| Untracked 
| 233|0x00000000ee900000, 0x00000000ee900000, 0x00000000eea00000|  0%| F|  |TAMS 0x00000000ee900000| PB 0x00000000ee900000| Untracked 
| 234|0x00000000eea00000, 0x00000000eea00000, 0x00000000eeb00000|  0%| F|  |TAMS 0x00000000eea00000| PB 0x00000000eea00000| Untracked 
| 235|0x00000000eeb00000, 0x00000000eeb00000, 0x00000000eec00000|  0%| F|  |TAMS 0x00000000eeb00000| PB 0x00000000eeb00000| Untracked 
| 236|0x00000000eec00000, 0x00000000eec00000, 0x00000000eed00000|  0%| F|  |TAMS 0x00000000eec00000| PB 0x00000000eec00000| Untracked 
| 237|0x00000000eed00000, 0x00000000eed00000, 0x00000000eee00000|  0%| F|  |TAMS 0x00000000eed00000| PB 0x00000000eed00000| Untracked 
| 238|0x00000000eee00000, 0x00000000eee00000, 0x00000000eef00000|  0%| F|  |TAMS 0x00000000eee00000| PB 0x00000000eee00000| Untracked 
| 239|0x00000000eef00000, 0x00000000eef00000, 0x00000000ef000000|  0%| F|  |TAMS 0x00000000eef00000| PB 0x00000000eef00000| Untracked 
| 240|0x00000000ef000000, 0x00000000ef000000, 0x00000000ef100000|  0%| F|  |TAMS 0x00000000ef000000| PB 0x00000000ef000000| Untracked 
| 241|0x00000000ef100000, 0x00000000ef100000, 0x00000000ef200000|  0%| F|  |TAMS 0x00000000ef100000| PB 0x00000000ef100000| Untracked 
| 242|0x00000000ef200000, 0x00000000ef200000, 0x00000000ef300000|  0%| F|  |TAMS 0x00000000ef200000| PB 0x00000000ef200000| Untracked 
| 243|0x00000000ef300000, 0x00000000ef300000, 0x00000000ef400000|  0%| F|  |TAMS 0x00000000ef300000| PB 0x00000000ef300000| Untracked 
| 244|0x00000000ef400000, 0x00000000ef400000, 0x00000000ef500000|  0%| F|  |TAMS 0x00000000ef400000| PB 0x00000000ef400000| Untracked 
| 245|0x00000000ef500000, 0x00000000ef500000, 0x00000000ef600000|  0%| F|  |TAMS 0x00000000ef500000| PB 0x00000000ef500000| Untracked 
| 246|0x00000000ef600000, 0x00000000ef600000, 0x00000000ef700000|  0%| F|  |TAMS 0x00000000ef600000| PB 0x00000000ef600000| Untracked 
| 247|0x00000000ef700000, 0x00000000ef700000, 0x00000000ef800000|  0%| F|  |TAMS 0x00000000ef700000| PB 0x00000000ef700000| Untracked 
| 248|0x00000000ef800000, 0x00000000ef800000, 0x00000000ef900000|  0%| F|  |TAMS 0x00000000ef800000| PB 0x00000000ef800000| Untracked 
| 249|0x00000000ef900000, 0x00000000ef900000, 0x00000000efa00000|  0%| F|  |TAMS 0x00000000ef900000| PB 0x00000000ef900000| Untracked 
| 250|0x00000000efa00000, 0x00000000efa00000, 0x00000000efb00000|  0%| F|  |TAMS 0x00000000efa00000| PB 0x00000000efa00000| Untracked 
| 251|0x00000000efb00000, 0x00000000efb00000, 0x00000000efc00000|  0%| F|  |TAMS 0x00000000efb00000| PB 0x00000000efb00000| Untracked 
| 252|0x00000000efc00000, 0x00000000efc00000, 0x00000000efd00000|  0%| F|  |TAMS 0x00000000efc00000| PB 0x00000000efc00000| Untracked 
| 253|0x00000000efd00000, 0x00000000efd00000, 0x00000000efe00000|  0%| F|  |TAMS 0x00000000efd00000| PB 0x00000000efd00000| Untracked 
| 254|0x00000000efe00000, 0x00000000efe00000, 0x00000000eff00000|  0%| F|  |TAMS 0x00000000efe00000| PB 0x00000000efe00000| Untracked 
| 255|0x00000000eff00000, 0x00000000eff00000, 0x00000000f0000000|  0%| F|  |TAMS 0x00000000eff00000| PB 0x00000000eff00000| Untracked 
| 256|0x00000000f0000000, 0x00000000f0000000, 0x00000000f0100000|  0%| F|  |TAMS 0x00000000f0000000| PB 0x00000000f0000000| Untracked 
| 257|0x00000000f0100000, 0x00000000f0100000, 0x00000000f0200000|  0%| F|  |TAMS 0x00000000f0100000| PB 0x00000000f0100000| Untracked 
| 258|0x00000000f0200000, 0x00000000f0200000, 0x00000000f0300000|  0%| F|  |TAMS 0x00000000f0200000| PB 0x00000000f0200000| Untracked 
| 259|0x00000000f0300000, 0x00000000f0300000, 0x00000000f0400000|  0%| F|  |TAMS 0x00000000f0300000| PB 0x00000000f0300000| Untracked 
| 260|0x00000000f0400000, 0x00000000f0400000, 0x00000000f0500000|  0%| F|  |TAMS 0x00000000f0400000| PB 0x00000000f0400000| Untracked 
| 261|0x00000000f0500000, 0x00000000f0500000, 0x00000000f0600000|  0%| F|  |TAMS 0x00000000f0500000| PB 0x00000000f0500000| Untracked 
| 262|0x00000000f0600000, 0x00000000f0600000, 0x00000000f0700000|  0%| F|  |TAMS 0x00000000f0600000| PB 0x00000000f0600000| Untracked 
| 263|0x00000000f0700000, 0x00000000f0700000, 0x00000000f0800000|  0%| F|  |TAMS 0x00000000f0700000| PB 0x00000000f0700000| Untracked 
| 264|0x00000000f0800000, 0x00000000f0800000, 0x00000000f0900000|  0%| F|  |TAMS 0x00000000f0800000| PB 0x00000000f0800000| Untracked 
| 265|0x00000000f0900000, 0x00000000f0900000, 0x00000000f0a00000|  0%| F|  |TAMS 0x00000000f0900000| PB 0x00000000f0900000| Untracked 
| 266|0x00000000f0a00000, 0x00000000f0a00000, 0x00000000f0b00000|  0%| F|  |TAMS 0x00000000f0a00000| PB 0x00000000f0a00000| Untracked 
| 267|0x00000000f0b00000, 0x00000000f0b00000, 0x00000000f0c00000|  0%| F|  |TAMS 0x00000000f0b00000| PB 0x00000000f0b00000| Untracked 
| 268|0x00000000f0c00000, 0x00000000f0c00000, 0x00000000f0d00000|  0%| F|  |TAMS 0x00000000f0c00000| PB 0x00000000f0c00000| Untracked 
| 269|0x00000000f0d00000, 0x00000000f0d00000, 0x00000000f0e00000|  0%| F|  |TAMS 0x00000000f0d00000| PB 0x00000000f0d00000| Untracked 
| 270|0x00000000f0e00000, 0x00000000f0e00000, 0x00000000f0f00000|  0%| F|  |TAMS 0x00000000f0e00000| PB 0x00000000f0e00000| Untracked 
| 271|0x00000000f0f00000, 0x00000000f0f00000, 0x00000000f1000000|  0%| F|  |TAMS 0x00000000f0f00000| PB 0x00000000f0f00000| Untracked 
| 272|0x00000000f1000000, 0x00000000f1000000, 0x00000000f1100000|  0%| F|  |TAMS 0x00000000f1000000| PB 0x00000000f1000000| Untracked 
| 273|0x00000000f1100000, 0x00000000f1100000, 0x00000000f1200000|  0%| F|  |TAMS 0x00000000f1100000| PB 0x00000000f1100000| Untracked 
| 274|0x00000000f1200000, 0x00000000f1200000, 0x00000000f1300000|  0%| F|  |TAMS 0x00000000f1200000| PB 0x00000000f1200000| Untracked 
| 275|0x00000000f1300000, 0x00000000f1300000, 0x00000000f1400000|  0%| F|  |TAMS 0x00000000f1300000| PB 0x00000000f1300000| Untracked 
| 276|0x00000000f1400000, 0x00000000f1400000, 0x00000000f1500000|  0%| F|  |TAMS 0x00000000f1400000| PB 0x00000000f1400000| Untracked 
| 277|0x00000000f1500000, 0x00000000f1500000, 0x00000000f1600000|  0%| F|  |TAMS 0x00000000f1500000| PB 0x00000000f1500000| Untracked 
| 278|0x00000000f1600000, 0x00000000f1600000, 0x00000000f1700000|  0%| F|  |TAMS 0x00000000f1600000| PB 0x00000000f1600000| Untracked 
| 279|0x00000000f1700000, 0x00000000f1700000, 0x00000000f1800000|  0%| F|  |TAMS 0x00000000f1700000| PB 0x00000000f1700000| Untracked 
| 280|0x00000000f1800000, 0x00000000f1800000, 0x00000000f1900000|  0%| F|  |TAMS 0x00000000f1800000| PB 0x00000000f1800000| Untracked 
| 464|0x00000000fd000000, 0x00000000fd100000, 0x00000000fd100000|100%| O|  |TAMS 0x00000000fd000000| PB 0x00000000fd000000| Untracked 
| 465|0x00000000fd100000, 0x00000000fd200000, 0x00000000fd200000|100%| O|  |TAMS 0x00000000fd100000| PB 0x00000000fd100000| Untracked 
| 466|0x00000000fd200000, 0x00000000fd200000, 0x00000000fd300000|  0%| F|  |TAMS 0x00000000fd200000| PB 0x00000000fd200000| Untracked 
| 467|0x00000000fd300000, 0x00000000fd300000, 0x00000000fd400000|  0%| F|  |TAMS 0x00000000fd300000| PB 0x00000000fd300000| Untracked 
| 468|0x00000000fd400000, 0x00000000fd400000, 0x00000000fd500000|  0%| F|  |TAMS 0x00000000fd400000| PB 0x00000000fd400000| Untracked 
| 469|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
| 470|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000| PB 0x00000000fd600000| Untracked 
| 471|0x00000000fd700000, 0x00000000fd700000, 0x00000000fd800000|  0%| F|  |TAMS 0x00000000fd700000| PB 0x00000000fd700000| Untracked 
| 472|0x00000000fd800000, 0x00000000fd800000, 0x00000000fd900000|  0%| F|  |TAMS 0x00000000fd800000| PB 0x00000000fd800000| Untracked 
| 473|0x00000000fd900000, 0x00000000fd900000, 0x00000000fda00000|  0%| F|  |TAMS 0x00000000fd900000| PB 0x00000000fd900000| Untracked 
| 474|0x00000000fda00000, 0x00000000fda00000, 0x00000000fdb00000|  0%| F|  |TAMS 0x00000000fda00000| PB 0x00000000fda00000| Untracked 
| 475|0x00000000fdb00000, 0x00000000fdb00000, 0x00000000fdc00000|  0%| F|  |TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Untracked 
| 476|0x00000000fdc00000, 0x00000000fdc00000, 0x00000000fdd00000|  0%| F|  |TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Untracked 
| 477|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| E|  |TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Complete 
| 478|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| E|CS|TAMS 0x00000000fde00000| PB 0x00000000fde00000| Complete 
| 479|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| E|CS|TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Complete 
| 480|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| E|CS|TAMS 0x00000000fe000000| PB 0x00000000fe000000| Complete 
| 481|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| E|CS|TAMS 0x00000000fe100000| PB 0x00000000fe100000| Complete 
| 482|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| E|CS|TAMS 0x00000000fe200000| PB 0x00000000fe200000| Complete 
| 483|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| E|CS|TAMS 0x00000000fe300000| PB 0x00000000fe300000| Complete 
| 484|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| E|CS|TAMS 0x00000000fe400000| PB 0x00000000fe400000| Complete 
| 485|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| E|CS|TAMS 0x00000000fe500000| PB 0x00000000fe500000| Complete 
| 486|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| E|CS|TAMS 0x00000000fe600000| PB 0x00000000fe600000| Complete 
| 487|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| E|CS|TAMS 0x00000000fe700000| PB 0x00000000fe700000| Complete 
| 488|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| E|CS|TAMS 0x00000000fe800000| PB 0x00000000fe800000| Complete 
| 489|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| E|CS|TAMS 0x00000000fe900000| PB 0x00000000fe900000| Complete 
| 490|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| E|CS|TAMS 0x00000000fea00000| PB 0x00000000fea00000| Complete 
| 491|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
| 492|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
| 493|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
| 494|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000| PB 0x00000000fee00000| Complete 
| 495|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
| 496|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000| PB 0x00000000ff000000| Complete 
| 497|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
| 498|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
| 499|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
| 500|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
| 501|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
| 502|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
| 503|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
| 504|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
| 505|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
| 506|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
| 507|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
| 508|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
| 509|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
| 510|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
| 511|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x000002beb16a0000,0x000002beb17a0000] _byte_map_base: 0x000002beb0fa0000

Marking Bits: (CMBitMap*) 0x000002be9966b080
 Bits: [0x000002beb17a0000, 0x000002beb1fa0000)

Polling page: 0x000002be974a0000

Metaspace:

Usage:
  Non-class:    124.08 MB used.
      Class:     18.38 MB used.
       Both:    142.46 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     125.12 MB ( 98%) committed,  2 nodes.
      Class space:      320.00 MB reserved,      19.38 MB (  6%) committed,  1 nodes.
             Both:      448.00 MB reserved,     144.50 MB ( 32%) committed. 

Chunk freelists:
   Non-Class:  2.06 MB
       Class:  12.55 MB
        Both:  14.62 MB

MaxMetaspaceSize: 384.00 MB
CompressedClassSpaceSize: 320.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 221.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 15.
num_arena_births: 4742.
num_arena_deaths: 1576.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2312.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 1788.
num_chunks_taken_from_freelist: 11911.
num_chunk_merges: 617.
num_chunk_splits: 7345.
num_chunks_enlarged: 4823.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=15776Kb max_used=15776Kb free=103392Kb
 bounds [0x000002bea98b0000, 0x000002beaa820000, 0x000002beb0d10000]
CodeHeap 'profiled nmethods': size=119104Kb used=33983Kb max_used=33983Kb free=85120Kb
 bounds [0x000002bea1d10000, 0x000002bea3e40000, 0x000002bea9160000]
CodeHeap 'non-nmethods': size=7488Kb used=3533Kb max_used=4705Kb free=3954Kb
 bounds [0x000002bea9160000, 0x000002bea9660000, 0x000002bea98b0000]
 total_blobs=18670 nmethods=17629 adapters=943
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 719.744 Thread 0x000002bed5776e20 30649   !   3       groovyjarjarantlr4.v4.runtime.atn.LexerActionExecutor::execute (166 bytes)
Event: 719.744 Thread 0x000002beb4f5a770 nmethod 30648 0x000002bea3e32310 code [0x000002bea3e324e0, 0x000002bea3e32888]
Event: 719.745 Thread 0x000002bed5776e20 nmethod 30649 0x000002bea3e32a90 code [0x000002bea3e32ce0, 0x000002bea3e33b30]
Event: 719.748 Thread 0x000002beb4f5a770 30651       3       groovyjarjarantlr4.v4.runtime.Parser::consume (215 bytes)
Event: 719.750 Thread 0x000002bed5776e20 30653       3       org.apache.groovy.parser.antlr4.GroovyLexer::exitParen (40 bytes)
Event: 719.754 Thread 0x000002bed5776e20 nmethod 30653 0x000002bea3e34090 code [0x000002bea3e342c0, 0x000002bea3e34cd8]
Event: 719.754 Thread 0x000002bed5776e20 30654       3       org.apache.groovy.parser.antlr4.GroovyLexer::enterParen (40 bytes)
Event: 719.756 Thread 0x000002beb4f5a770 nmethod 30651 0x000002bea3e34f90 code [0x000002bea3e35300, 0x000002bea3e36e38]
Event: 719.757 Thread 0x000002bed5776e20 nmethod 30654 0x000002bea3e37610 code [0x000002bea3e37860, 0x000002bea3e387e0]
Event: 719.765 Thread 0x000002beb4f5a770 30655       3       groovyjarjarantlr4.v4.runtime.atn.SemanticContext$PrecedencePredicate::evalPrecedence (18 bytes)
Event: 719.766 Thread 0x000002beb4f5a770 nmethod 30655 0x000002bea3e38c10 code [0x000002bea3e38dc0, 0x000002bea3e39010]
Event: 719.776 Thread 0x000002beb4f5a770 30657   !   3       org.apache.groovy.parser.antlr4.GroovyParser::blockStatement (178 bytes)
Event: 719.776 Thread 0x000002bed5776e20 30658       3       org.apache.groovy.parser.antlr4.GroovyParser$BlockStatementContext::<init> (7 bytes)
Event: 719.777 Thread 0x000002bed5776e20 nmethod 30658 0x000002bea3e39110 code [0x000002bea3e392e0, 0x000002bea3e39600]
Event: 719.778 Thread 0x000002beb4f5a770 nmethod 30657 0x000002bea3e39790 code [0x000002bea3e39a40, 0x000002bea3e3aa38]
Event: 719.797 Thread 0x000002beb4f5a770 30662       3       groovyjarjarantlr4.v4.runtime.atn.ParserATNSimulator::reportContextSensitivity (31 bytes)
Event: 719.799 Thread 0x000002beb4f5a770 nmethod 30662 0x000002bea3e3b010 code [0x000002bea3e3b200, 0x000002bea3e3b868]
Event: 719.805 Thread 0x000002beb4f5a770 30665       3       groovyjarjarantlr4.v4.runtime.atn.ATNConfigSet::setOutermostConfigSet (50 bytes)
Event: 719.805 Thread 0x000002beb4f5a770 nmethod 30665 0x000002bea3e3ba90 code [0x000002bea3e3bc40, 0x000002bea3e3be88]
Event: 720.862 Thread 0x000002bed5773e70 30628       3       org.gradle.internal.resource.transport.http.HttpClientHelper::<init> (48 bytes)

GC Heap History (20 events):
Event: 413.336 GC heap before
{Heap before GC invocations=74 (full 0):
 garbage-first heap   total 322560K, used 291186K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 148 young (151552K), 11 survivors (11264K)
 Metaspace       used 99027K, committed 101376K, reserved 458752K
  class space    used 12853K, committed 14016K, reserved 327680K
}
Event: 413.436 GC heap after
{Heap after GC invocations=75 (full 0):
 garbage-first heap   total 322560K, used 158308K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 99027K, committed 101376K, reserved 458752K
  class space    used 12853K, committed 14016K, reserved 327680K
}
Event: 437.205 GC heap before
{Heap before GC invocations=76 (full 0):
 garbage-first heap   total 322560K, used 286308K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 142 young (145408K), 17 survivors (17408K)
 Metaspace       used 105238K, committed 107072K, reserved 458752K
  class space    used 13473K, committed 14400K, reserved 327680K
}
Event: 437.267 GC heap after
{Heap after GC invocations=77 (full 0):
 garbage-first heap   total 322560K, used 163754K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 105238K, committed 107072K, reserved 458752K
  class space    used 13473K, committed 14400K, reserved 327680K
}
Event: 447.798 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total 322560K, used 288682K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 137 young (140288K), 14 survivors (14336K)
 Metaspace       used 105334K, committed 107136K, reserved 458752K
  class space    used 13473K, committed 14400K, reserved 327680K
}
Event: 447.849 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total 322560K, used 169248K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 105334K, committed 107136K, reserved 458752K
  class space    used 13473K, committed 14400K, reserved 327680K
}
Event: 457.874 GC heap before
{Heap before GC invocations=78 (full 0):
 garbage-first heap   total 322560K, used 287008K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 132 young (135168K), 17 survivors (17408K)
 Metaspace       used 115596K, committed 117312K, reserved 458752K
  class space    used 14751K, committed 15552K, reserved 327680K
}
Event: 457.921 GC heap after
{Heap after GC invocations=79 (full 0):
 garbage-first heap   total 322560K, used 171428K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 115596K, committed 117312K, reserved 458752K
  class space    used 14751K, committed 15552K, reserved 327680K
}
Event: 474.718 GC heap before
{Heap before GC invocations=79 (full 0):
 garbage-first heap   total 322560K, used 290212K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 130 young (133120K), 14 survivors (14336K)
 Metaspace       used 121034K, committed 122816K, reserved 458752K
  class space    used 15480K, committed 16320K, reserved 327680K
}
Event: 475.039 GC heap after
{Heap after GC invocations=80 (full 0):
 garbage-first heap   total 322560K, used 173518K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 121034K, committed 122816K, reserved 458752K
  class space    used 15480K, committed 16320K, reserved 327680K
}
Event: 518.948 GC heap before
{Heap before GC invocations=80 (full 0):
 garbage-first heap   total 322560K, used 293326K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 128 young (131072K), 14 survivors (14336K)
 Metaspace       used 133415K, committed 135360K, reserved 458752K
  class space    used 17048K, committed 17984K, reserved 327680K
}
Event: 518.996 GC heap after
{Heap after GC invocations=81 (full 0):
 garbage-first heap   total 322560K, used 188852K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 133415K, committed 135360K, reserved 458752K
  class space    used 17048K, committed 17984K, reserved 327680K
}
Event: 519.214 GC heap before
{Heap before GC invocations=81 (full 0):
 garbage-first heap   total 322560K, used 190900K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 16 survivors (16384K)
 Metaspace       used 133592K, committed 135488K, reserved 458752K
  class space    used 17083K, committed 17984K, reserved 327680K
}
Event: 519.247 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total 322560K, used 193281K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 133592K, committed 135488K, reserved 458752K
  class space    used 17083K, committed 17984K, reserved 327680K
}
Event: 529.015 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total 336896K, used 307969K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 107 young (109568K), 3 survivors (3072K)
 Metaspace       used 139281K, committed 141184K, reserved 458752K
  class space    used 17818K, committed 18688K, reserved 327680K
}
Event: 529.028 GC heap after
{Heap after GC invocations=84 (full 0):
 garbage-first heap   total 336896K, used 207617K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 139281K, committed 141184K, reserved 458752K
  class space    used 17818K, committed 18688K, reserved 327680K
}
Event: 535.579 GC heap before
{Heap before GC invocations=84 (full 0):
 garbage-first heap   total 336896K, used 308993K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 109 young (111616K), 9 survivors (9216K)
 Metaspace       used 143972K, committed 145984K, reserved 458752K
  class space    used 18554K, committed 19520K, reserved 327680K
}
Event: 535.599 GC heap after
{Heap after GC invocations=85 (full 0):
 garbage-first heap   total 336896K, used 214841K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 143972K, committed 145984K, reserved 458752K
  class space    used 18554K, committed 19520K, reserved 327680K
}
Event: 705.034 GC heap before
{Heap before GC invocations=85 (full 0):
 garbage-first heap   total 336896K, used 304953K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 101 young (103424K), 12 survivors (12288K)
 Metaspace       used 145786K, committed 147904K, reserved 458752K
  class space    used 18822K, committed 19840K, reserved 327680K
}
Event: 705.367 GC heap after
{Heap after GC invocations=86 (full 0):
 garbage-first heap   total 336896K, used 215209K [0x00000000e0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 145786K, committed 147904K, reserved 458752K
  class space    used 18822K, committed 19840K, reserved 327680K
}

Dll operation events (15 events):
Event: 0.103 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
Event: 0.346 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
Event: 0.536 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 0.564 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
Event: 0.605 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
Event: 0.632 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
Event: 0.643 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
Event: 1.890 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
Event: 2.503 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
Event: 2.916 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 2.984 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 8.082 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
Event: 8.117 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
Event: 9.296 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
Event: 9.942 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 719.749 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea23ca35f sp=0x000000ce559f4cb0
Event: 719.749 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f41d8 mode 0
Event: 719.759 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea2d708be sp=0x000000ce559f59d0
Event: 719.759 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f4ed0 mode 0
Event: 719.775 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea309699b sp=0x000000ce559f4560
Event: 719.775 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f3aa8 mode 0
Event: 719.775 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea2d7087c sp=0x000000ce559f51a0
Event: 719.775 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f46a0 mode 0
Event: 719.778 Thread 0x000002bed29092d0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002bea9a02488 relative=0x0000000000000688
Event: 719.778 Thread 0x000002bed29092d0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002bea9a02488 method=groovyjarjarantlr4.v4.runtime.atn.ParserATNSimulator.computeTargetState(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;Lgroovyj
Event: 719.778 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea9a02488 sp=0x000000ce559f5480
Event: 719.778 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b46a2 sp=0x000000ce559f5478 mode 2
Event: 719.800 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea3096b4c sp=0x000000ce559f4bf0
Event: 719.800 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f4138 mode 0
Event: 719.820 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea2d708be sp=0x000000ce559f54a0
Event: 719.820 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f49a0 mode 0
Event: 719.838 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea2d708be sp=0x000000ce559f54a0
Event: 719.838 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f49a0 mode 0
Event: 719.854 Thread 0x000002bed29092d0 DEOPT PACKING pc=0x000002bea309699b sp=0x000000ce559f5180
Event: 719.854 Thread 0x000002bed29092d0 DEOPT UNPACKING pc=0x000002bea91b4e42 sp=0x000000ce559f46c8 mode 0

Classes loaded (20 events):
Event: 486.873 Loading class java/util/EnumMap$EnumMapIterator done
Event: 486.873 Loading class java/util/EnumMap$ValueIterator done
Event: 520.682 Loading class java/time/temporal/WeekFields
Event: 520.693 Loading class java/time/temporal/WeekFields done
Event: 520.711 Loading class java/time/temporal/WeekFields$ComputedDayOfField
Event: 520.739 Loading class java/time/temporal/WeekFields$ComputedDayOfField done
Event: 523.438 Loading class java/lang/Override
Event: 523.439 Loading class java/lang/Override done
Event: 529.763 Loading class java/lang/AbstractMethodError
Event: 529.764 Loading class java/lang/AbstractMethodError done
Event: 531.453 Loading class java/util/Collections$ReverseComparator
Event: 531.454 Loading class java/util/Collections$ReverseComparator done
Event: 531.455 Loading class java/util/Collections$ReverseComparator2
Event: 531.455 Loading class java/util/Collections$ReverseComparator2 done
Event: 534.031 Loading class java/util/TreeMap$KeySpliterator
Event: 534.033 Loading class java/util/TreeMap$KeySpliterator done
Event: 534.120 Loading class java/util/TreeMap$ValueSpliterator
Event: 534.121 Loading class java/util/TreeMap$ValueSpliterator done
Event: 534.831 Loading class java/net/InetAddress$CachedLocalHost
Event: 534.833 Loading class java/net/InetAddress$CachedLocalHost done

Classes unloaded (20 events):
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb677d000 'java/lang/invoke/LambdaForm$MH+0x000002beb677d000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb677cc00 'java/lang/invoke/LambdaForm$MH+0x000002beb677cc00'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb677c800 'java/lang/invoke/LambdaForm$BMH+0x000002beb677c800'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672c800 'java/lang/invoke/LambdaForm$MH+0x000002beb672c800'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672c400 'java/lang/invoke/LambdaForm$MH+0x000002beb672c400'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672c000 'java/lang/invoke/LambdaForm$MH+0x000002beb672c000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672bc00 'java/lang/invoke/LambdaForm$MH+0x000002beb672bc00'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672b800 'java/lang/invoke/LambdaForm$MH+0x000002beb672b800'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672b400 'java/lang/invoke/LambdaForm$MH+0x000002beb672b400'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb672b000 'java/lang/invoke/LambdaForm$MH+0x000002beb672b000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb6646000 'java/lang/invoke/LambdaForm$MH+0x000002beb6646000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb64fc000 'java/lang/invoke/LambdaForm$MH+0x000002beb64fc000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb6483000 'java/lang/invoke/LambdaForm$MH+0x000002beb6483000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb636d400 'java/lang/invoke/LambdaForm$MH+0x000002beb636d400'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb636d000 'java/lang/invoke/LambdaForm$MH+0x000002beb636d000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb6238c00 'java/lang/invoke/LambdaForm$MH+0x000002beb6238c00'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb6234800 'java/lang/invoke/LambdaForm$MH+0x000002beb6234800'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb6234400 'java/lang/invoke/LambdaForm$MH+0x000002beb6234400'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb61bb000 'java/lang/invoke/LambdaForm$MH+0x000002beb61bb000'
Event: 522.016 Thread 0x000002beb4f14cc0 Unloading class 0x000002beb603c000 'java/lang/invoke/LambdaForm$DMH+0x000002beb603c000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 709.190 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fef7d5b0}> (0x00000000fef7d5b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 709.192 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fef7ec50}> (0x00000000fef7ec50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 709.195 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fef802e0}> (0x00000000fef802e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 709.197 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fef81bb0}> (0x00000000fef81bb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 711.140 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fe2e21d8}> (0x00000000fe2e21d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 711.141 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fe2e3878}> (0x00000000fe2e3878) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 711.144 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fe2e4f08}> (0x00000000fe2e4f08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 711.146 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fe2e6578}> (0x00000000fe2e6578) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 713.996 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fd428a78}> (0x00000000fd428a78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 714.000 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fd42a118}> (0x00000000fd42a118) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 714.002 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fd42b7a8}> (0x00000000fd42b7a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 714.004 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fd42ce18}> (0x00000000fd42ce18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 718.845 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f0e7e780}> (0x00000000f0e7e780) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 718.846 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f0e7fe20}> (0x00000000f0e7fe20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 718.847 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f0e814b0}> (0x00000000f0e814b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 718.847 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f0e82b20}> (0x00000000f0e82b20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 719.566 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f0008b40}> (0x00000000f0008b40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 719.568 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f000a1e0}> (0x00000000f000a1e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 719.569 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f000b870}> (0x00000000f000b870) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 719.570 Thread 0x000002bed29092d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000f000cee0}> (0x00000000f000cee0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 704.769 Executing VM operation: Cleanup done
Event: 705.028 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 705.368 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 706.377 Executing VM operation: Cleanup
Event: 706.378 Executing VM operation: Cleanup done
Event: 707.386 Executing VM operation: Cleanup
Event: 707.387 Executing VM operation: Cleanup done
Event: 709.398 Executing VM operation: Cleanup
Event: 709.414 Executing VM operation: Cleanup done
Event: 710.425 Executing VM operation: Cleanup
Event: 710.425 Executing VM operation: Cleanup done
Event: 711.437 Executing VM operation: Cleanup
Event: 711.438 Executing VM operation: Cleanup done
Event: 713.452 Executing VM operation: Cleanup
Event: 713.452 Executing VM operation: Cleanup done
Event: 714.463 Executing VM operation: Cleanup
Event: 714.478 Executing VM operation: Cleanup done
Event: 719.533 Executing VM operation: Cleanup
Event: 719.533 Executing VM operation: Cleanup done
Event: 719.857 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea27f7490
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2976690
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2987410
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2987890
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2987d90
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2988790
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a51f10
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a60190
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a61390
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a62d10
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a63d10
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2a67410
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2aa6410
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2aac590
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2b09e10
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2b0d190
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea2b15c10
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea34b2890
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea34b4490
Event: 522.751 Thread 0x000002beb4f14cc0 flushing  nmethod 0x000002bea35ea510

Events (20 events):
Event: 719.001 Thread 0x000002beca11e6e0 Thread exited: 0x000002beca11e6e0
Event: 719.007 Thread 0x000002bed3360090 Thread exited: 0x000002bed3360090
Event: 719.007 Thread 0x000002beca11f400 Thread exited: 0x000002beca11f400
Event: 719.019 Thread 0x000002bed335fa00 Thread exited: 0x000002bed335fa00
Event: 719.019 Thread 0x000002bed335f370 Thread exited: 0x000002bed335f370
Event: 719.034 Thread 0x000002bed335ece0 Thread exited: 0x000002bed335ece0
Event: 719.050 Thread 0x000002bed2909960 Thread exited: 0x000002bed2909960
Event: 719.053 Thread 0x000002bed290ad10 Thread exited: 0x000002bed290ad10
Event: 719.445 Thread 0x000002bed45645a0 Thread added: 0x000002bed335ece0
Event: 719.452 Thread 0x000002bed45645a0 Thread added: 0x000002bed335f370
Event: 719.462 Thread 0x000002bed29092d0 Thread added: 0x000002bed335fa00
Event: 719.464 Thread 0x000002bed29092d0 Thread added: 0x000002bed3360090
Event: 719.465 Thread 0x000002bed29092d0 Thread added: 0x000002bed3360db0
Event: 719.477 Thread 0x000002bed29092d0 Thread added: 0x000002bed4c3e3f0
Event: 719.484 Thread 0x000002bed29092d0 Thread added: 0x000002bed4c3ea80
Event: 719.526 Thread 0x000002bed29092d0 Thread added: 0x000002bed4c3fe30
Event: 719.534 Thread 0x000002bed29092d0 Thread added: 0x000002bed4c3f7a0
Event: 719.543 Thread 0x000002bed29092d0 Thread added: 0x000002bed4c3f110
Event: 719.593 Thread 0x000002beb4f5a770 Thread added: 0x000002bed5773e70
Event: 719.594 Thread 0x000002beb4f5a770 Thread added: 0x000002bed5776e20


Dynamic libraries:
0x00007ff794cf0000 - 0x00007ff794cfe000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe
0x00007ffe23080000 - 0x00007ffe232e6000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdf9f40000 - 0x00007ffdf9f5b000 	C:\Program Files\Norton\Suite\aswhook.dll
0x00007ffe21cd0000 - 0x00007ffe21d99000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe20830000 - 0x00007ffe20bfc000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe1cf70000 - 0x00007ffe1d00e000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffe20210000 - 0x00007ffe2035b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe0c710000 - 0x00007ffe0c72e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\VCRUNTIME140.dll
0x00007ffe0fed0000 - 0x00007ffe0fee8000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jli.dll
0x00007ffe22e70000 - 0x00007ffe2303a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe206d0000 - 0x00007ffe206f7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe01860000 - 0x00007ffe01afa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffe221f0000 - 0x00007ffe2221b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe22540000 - 0x00007ffe225e9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe20360000 - 0x00007ffe20492000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe20620000 - 0x00007ffe206c3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe214a0000 - 0x00007ffe214d0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe0fec0000 - 0x00007ffe0fecc000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\vcruntime140_1.dll
0x00007ffdf8a20000 - 0x00007ffdf8aad000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\msvcp140.dll
0x00007ffd803b0000 - 0x00007ffd81140000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server\jvm.dll
0x00007ffe22480000 - 0x00007ffe22532000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe22250000 - 0x00007ffe222f6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe220c0000 - 0x00007ffe221d6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe225f0000 - 0x00007ffe22664000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe1fea0000 - 0x00007ffe1fefe000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe1dbc0000 - 0x00007ffe1dbcb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe179e0000 - 0x00007ffe17a16000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe1fe80000 - 0x00007ffe1fe94000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe1efd0000 - 0x00007ffe1efea000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe0ca30000 - 0x00007ffe0ca3a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jimage.dll
0x00007ffe1a7b0000 - 0x00007ffe1a9f1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe20f20000 - 0x00007ffe212a4000 	C:\WINDOWS\System32\combase.dll
0x00007ffe212b0000 - 0x00007ffe21390000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe13090000 - 0x00007ffe130c9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe20700000 - 0x00007ffe20799000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe04b00000 - 0x00007ffe04b0f000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\instrument.dll
0x00007ffe00d10000 - 0x00007ffe00d2f000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.dll
0x00007ffe22740000 - 0x00007ffe22e6d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe204a0000 - 0x00007ffe20614000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffe1de50000 - 0x00007ffe1e6a6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe213a0000 - 0x00007ffe2148f000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe22670000 - 0x00007ffe226d9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe20120000 - 0x00007ffe2014f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdd4760000 - 0x00007ffdd4837000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\jsvml.dll
0x00007ffe01c00000 - 0x00007ffe01c18000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\zip.dll
0x00007ffe0d5a0000 - 0x00007ffe0d5b0000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\net.dll
0x00007ffe19530000 - 0x00007ffe1964e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe1f540000 - 0x00007ffe1f5aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe01b90000 - 0x00007ffe01ba6000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\nio.dll
0x00007ffe00d00000 - 0x00007ffe00d10000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\verify.dll
0x00007ffda4370000 - 0x00007ffda4397000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffdd45c0000 - 0x00007ffdd4704000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffdf8a10000 - 0x00007ffdf8a1a000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management.dll
0x00007ffde33d0000 - 0x00007ffde33db000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\management_ext.dll
0x00007ffe221e0000 - 0x00007ffe221e8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe1f7f0000 - 0x00007ffe1f80c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe1ef30000 - 0x00007ffe1ef6a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe1f5e0000 - 0x00007ffe1f60b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe200f0000 - 0x00007ffe20116000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffe1f7e0000 - 0x00007ffe1f7ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffe1ea80000 - 0x00007ffe1eab3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe22730000 - 0x00007ffe2273a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffde3380000 - 0x00007ffde3389000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\extnet.dll
0x00007ffde2b70000 - 0x00007ffde2b7e000 	C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\sunmscapi.dll
0x00007ffe20cc0000 - 0x00007ffe20e37000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffe1f9f0000 - 0x00007ffe1fa20000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffe1f9a0000 - 0x00007ffe1f9df000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffe1acc0000 - 0x00007ffe1acc8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffe1eac0000 - 0x00007ffe1ebe7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffe191e0000 - 0x00007ffe191eb000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe1a410000 - 0x00007ffe1a496000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffe01000000 - 0x00007ffe01018000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffe00f10000 - 0x00007ffe00f22000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffe004f0000 - 0x00007ffe00520000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffe00ee0000 - 0x00007ffe00f00000 	C:\WINDOWS\system32\wshbth.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Norton\Suite;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -XX:MaxMetaspaceSize=384m -XX:+HeapDumpOnOutOfMemoryError -Xms256m -Xmx512m -Dfile.encoding=utf8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\gradle-daemon-main-8.9.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 335544320                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 268435456                                 {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 536870912                                 {product} {command line}
   size_t MaxMetaspaceSize                         = 402653184                                 {product} {command line}
   size_t MaxNewSize                               = 321912832                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 268435456                                 {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 536870912                              {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
PATH=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.402.6-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\MongoDB\Server\8.0\bin;C:\Program Files\Git\cmd;C:\Program Files\CMake\bin;C:\Program Files\CMake\bin;:\Users\gokul\AppData\Roaming\npm\node_modules;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk
\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk
\tools\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;;C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=gokul
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 2 days 1:31 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x8600106, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, rdpid, f16c
Processor Information for the first 16 processors :
  Max Mhz: 2901, Current Mhz: 2901, Mhz Limit: 2901

Memory: 4k page, system-wide physical 7599M (366M free)
TotalPageFile size 31151M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 277M, peak: 600M
current process commit charge ("private bytes"): 770M, peak: 778M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
