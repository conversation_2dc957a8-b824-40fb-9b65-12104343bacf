import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';


// Placeholder chapter data (replace with actual data later, could be different from syllabus)
const videoChapterData = {
  Physics: ['Video Mechanics', 'Video Thermodynamics', 'Video Electromagnetism', 'Video Optics', 'Video Modern Physics'],
  Chemistry: ['Video Atomic Structure', 'Video Chemical Bonding', 'Video Organic Chemistry', 'Video Physical Chemistry', 'Video Inorganic Chemistry'],
  Biology: ['Video Cell Biology', 'Video Genetics', 'Video Ecology', 'Video Human Physiology', 'Video Plant Physiology'],
  Science: ['Video General Science 1', 'Video General Science 2', 'Video General Science 3', 'Video Environmental Science'], // Example
  Math: ['Video Algebra', 'Video Calculus', 'Video Geometry', 'Video Trigonometry', 'Video Statistics'],
};

const VideoChapterScreen = ({ route, navigation }) => {
  const { category } = route.params; // Get category
  const darkMode = false;

  // Define categories that are coming soon
  const comingSoonCategories = ['Physics', 'Chemistry', 'Biology', 'Math'];
  const isComingSoon = comingSoonCategories.includes(category);

  // Get chapters only if the category is NOT coming soon
  const chapters = !isComingSoon ? (videoChapterData[category] || []) : [];

  const handleChapterPress = (chapter) => {
    // This function will only be called if the category is NOT coming soon
    // Navigate to the actual video list/player screen
    // For now, just log it
    console.log(`Selected Video Chapter: ${chapter} in Category: ${category}`);
    // Example navigation:
    // navigation.navigate('VideoPlayer', { category, chapter });
    alert(`Navigate to Video Player/List for ${chapter}`);
  };

  // Only render the list if the category is not coming soon
  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.itemContainer, darkMode ? styles.darkItem : styles.lightItem]}
      onPress={() => handleChapterPress(item)}
    >
      <Text style={[styles.itemText, darkMode ? styles.darkText : styles.lightText]}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, darkMode ? styles.darkMode : styles.lightMode]}>
      <Text style={[styles.title, darkMode ? styles.darkText : styles.lightText]}>{category} Video Chapters</Text>
      {isComingSoon ? (
         <Text style={[styles.noChaptersText, darkMode ? styles.darkText : styles.lightText]}>
           Video content for {category} is coming soon!
         </Text>
      ) : chapters.length > 0 ? (
        <FlatList
          data={chapters}
          renderItem={renderItem}
          keyExtractor={(item) => item}
          style={styles.list}
        />
      ) : (
         // Fallback message if not coming soon but still no chapters
        <Text style={[styles.noChaptersText, darkMode ? styles.darkText : styles.lightText]}>
          Video Chapters for {category} will be available soon.
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  itemContainer: {
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    borderWidth: 1,
  },
  itemText: {
    fontSize: 18,
  },
  noChaptersText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },
  lightMode: { backgroundColor: '#f8f9fa' },
  darkMode: { backgroundColor: '#121212' },
  lightItem: { borderColor: '#ddd', backgroundColor: '#fff' },
  darkItem: { borderColor: '#555', backgroundColor: '#333' },
  lightText: { color: '#333' },
  darkText: { color: '#fff' },
});

export default VideoChapterScreen;
