import 'intl-pluralrules'; // Import the polyfill
import { registerRootComponent } from 'expo';
// Removed unused StatusBar import

import App from './App';

console.log('Project initialized successfully.');
// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
