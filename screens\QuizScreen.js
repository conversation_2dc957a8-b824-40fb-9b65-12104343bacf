import React, { useEffect, useState, useRef, useMemo } from 'react';
// ScrollView was already imported, no change needed here, but ensuring FlatList is removed if not used.
// Let's check if FlatList is still used... No, it's not. Let's remove it.
import { View, Text, TouchableOpacity, StyleSheet, Animated, Image, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import BannerAdComponent from '../components/BannerAdComponent';
import InterstitialAdComponent from '../components/InterstitialAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

// Import images
import neetImage from '../assets/images/neet.png';
// import scienceImage from '../assets/science.png'; // Removed unused import
import mathImage from '../assets/images/math.png';
// import historyImage from '../assets/history.png'; // Removed unused import
// import technologyImage from '../assets/technology.png'; // Removed unused import
import physicsImage from '../assets/images/physics.png';
import chemistryImage from '../assets/images/chemistry.png';
import biologyImage from '../assets/images/biology.png';

const QuizScreen = ({ navigation }) => {
  const soundEnabled = true;
  const [highestLevel, setHighestLevel] = useState(1);
  const soundRef = useRef(null);

  const categories = useMemo(() => [
    { name: 'NEET', colors: ['#ff5733', '#ff884d'], image: neetImage, description: 'Prepare for NEET with subject-wise quizzes.' },
    { name: 'Physics', colors: ['#6200ea', '#9575cd'], image: physicsImage, description: 'Dive into the laws of physics.' },
    { name: 'Chemistry', colors: ['#ff6f61', '#ffab91'], image: chemistryImage, description: 'Understand the world of chemistry.' },
    { name: 'Biology', colors: ['#4caf50', '#81c784'], image: biologyImage, description: 'Explore the wonders of biology.' },
    // Removed Science, Math, History, Technology from this main list
  ], []);

  // Filtered categories for Syllabus and Video Learning - This needs adjustment if those categories are removed entirely
  // Let's assume for now Syllabus/Video should still show Science/Math if they exist elsewhere or are hardcoded
  // Re-defining the source for syllabus/video categories explicitly
  const syllabusAndVideoSourceCategories = useMemo(() => [
    { name: 'Physics', colors: ['#6200ea', '#9575cd'], image: physicsImage, description: 'Dive into the laws of physics.' },
    { name: 'Chemistry', colors: ['#ff6f61', '#ffab91'], image: chemistryImage, description: 'Understand the world of chemistry.' },
    { name: 'Biology', colors: ['#4caf50', '#81c784'], image: biologyImage, description: 'Explore the wonders of biology.' },
    // Removed Science from this source list
    { name: 'Math', colors: ['#3357ff', '#6699ff'], image: mathImage, description: 'Sharpen your problem-solving skills.' }, // Keep Math for Syllabus/Video
  ], []);

  // Filter now only includes categories present in the source list
  const syllabusAndVideoCategories = useMemo(() => syllabusAndVideoSourceCategories.filter(category =>
    ['Physics', 'Chemistry', 'Biology', 'Math'].includes(category.name)
  ), [syllabusAndVideoSourceCategories]);

  // Load sound effect on mount
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates on unmounted component

    const loadSound = async () => {
      if (soundEnabled) {
        try {
          console.log('Loading sound...');
          const { sound } = await Audio.Sound.createAsync(require('../assets/audios/click.mp3'));
          if (isMounted) {
            soundRef.current = sound;
            console.log('Sound loaded successfully.');
          } else {
            // Component unmounted before sound loaded, unload it
            console.log('Component unmounted during sound load, unloading.');
            sound.unloadAsync();
          }
        } catch (error) {
          console.error('Error loading sound:', error);
        }
      }
    };

    const loadProgress = async () => {
      try {
        const storedLevel = await AsyncStorage.getItem('highestLevel');
        setHighestLevel(storedLevel ? Number(storedLevel) : 1);
      } catch (error) {
        console.error('Error loading level progress:', error);
      }
    };
    loadProgress();
    loadSound(); // Load sound on mount

    // Cleanup function for sound
    return () => {
      isMounted = false; // Set flag when unmounting
      if (soundRef.current) {
        console.log('Unloading sound from QuizScreen');
        soundRef.current.unloadAsync();
        soundRef.current = null; // Clear the ref
      }
    };
  }, [soundEnabled]); // Reload sound if soundEnabled changes

  const playSound = async () => {
    // Only play if sound is enabled and loaded
    if (soundEnabled && soundRef.current) {
      try {
        await soundRef.current.replayAsync();
      } catch (error) {
        console.error('Error playing sound:', error);
        // Attempt to reload sound if playback fails? Or just log.
        // Maybe the sound got unloaded unexpectedly.
      }
    } else if (soundEnabled && !soundRef.current) {
       console.log('Sound enabled but not loaded, skipping playback.');
       // Optionally try loading it again here?
    }
  };

  // Renamed openLevelsPage to handleCategoryPress and added sectionTitle parameter
  const handleCategoryPress = (category, sectionTitle) => {
    playSound();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Updated list to remove 'Science'
    const chapterBasedCategories = ['Physics', 'Chemistry', 'Biology', 'Math'];

    if (sectionTitle === 'Video Learning' && chapterBasedCategories.includes(category.name)) {
      // Navigate to VideoChapterScreen for specific categories under Video Learning
      navigation.navigate('VideoChapterScreen', { category: category.name });
    } else if (sectionTitle === 'Syllabus' && chapterBasedCategories.includes(category.name)) {
      // Navigate to ChapterScreen for specific categories under Syllabus
      navigation.navigate('ChapterScreen', { category: category.name, sectionTitle: sectionTitle }); // Keep sectionTitle here if ChapterScreen needs it
    } else {
      // Navigate to QuizLevels for Mark Tests or other categories
      navigation.navigate('QuizLevels', { category: category.name, highestLevel }); // Pass highestLevel for quiz levels
    }
  };

  // Updated renderHorizontalSection to handle Syllabus and Video Learning colors
  const renderHorizontalSection = (title, data) => {
    // Define new colors specifically for the Syllabus section
    const syllabusColors = {
      Physics: ['#2196F3', '#64b5f6'], // Blue gradient
      Chemistry: ['#FFC107', '#ffd54f'], // Amber gradient
      Biology: ['#009688', '#4db6ac'], // Teal gradient
      // Science: ['#8BC34A', '#aed581'], // Removed Science color
      Math: ['#9C27B0', '#ce93d8'], // Purple gradient
    };

    // Define new colors specifically for the Video Learning section
    const videoLearningColors = {
      Physics: ['#CDDC39', '#dce775'], // Lime green gradient (New)
      Chemistry: ['#3F51B5', '#7986cb'], // Indigo gradient
      Biology: ['#E91E63', '#f06292'], // Pink gradient
      // Science: ['#00BCD4', '#4dd0e1'], // Removed Science color
      Math: ['#4CAF50', '#81c784'], // Green gradient
    };

    // Determine the data to display, applying section-specific colors
    const displayData = data.map(item => {
      let itemColors = item.colors; // Start with original colors
      if (title === 'Syllabus' && syllabusColors[item.name]) {
        itemColors = syllabusColors[item.name];
      } else if (title === 'Video Learning' && videoLearningColors[item.name]) {
        itemColors = videoLearningColors[item.name];
      }
      return { ...item, colors: itemColors };
    });

    return (
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, styles.lightText]}>{title}</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {displayData.map((item) => ( // Use the potentially modified displayData
            <TouchableOpacity
              key={item.name}
              style={styles.horizontalItem}
              onPress={() => handleCategoryPress(item, title)}> {/* Pass section title */}
              <LinearGradient colors={item.colors} style={styles.gradient}>
                <Image source={item.image} style={styles.image} resizeMode="contain" />
                <Text style={[styles.buttonText, styles.lightText]}>
                  {item.name || 'Unknown'}
                </Text>
                <Text style={[styles.description, styles.lightText]}>
                  {item.description || 'No description'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  }; // Make sure this closing brace is correct

  return (
    // Wrap content in a ScrollView for vertical scrolling
    <View style={{ flex: 1 }}>
      <ScrollView style={[styles.container, styles.lightMode]}>
        {/* Removed the Title Text */}
        {renderHorizontalSection('Mock Tests', categories)}
        {renderHorizontalSection('Syllabus', syllabusAndVideoCategories)}
        {renderHorizontalSection('Video Learning', syllabusAndVideoCategories)}
      </ScrollView>

      {/* Enhanced Banner Ad at the bottom */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.QUIZ_BANNER}
        />
      </View>

      {/* Interstitial Ad Component (invisible, manages interstitial ads) */}
      <InterstitialAdComponent
        placement={AD_PLACEMENTS.QUIZ_INTERSTITIAL}
        autoShow={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  // Adjust container style if needed for ScrollView, e.g., remove flex: 1 if it causes issues
  container: { flex: 1 }, // Keep flex: 1 for ScrollView to take available space
  // Add padding inside ScrollView content if needed, or keep on sections
  // title style is still here but unused, can be removed later if desired
  title: { fontSize: 28, fontWeight: 'bold', marginBottom: 20, textAlign: 'center' },
  sectionContainer: { marginBottom: 20, paddingVertical: 10 }, // Added paddingVertical for spacing within ScrollView
  sectionTitle: { fontSize: 22, fontWeight: '600', marginLeft: 16, marginBottom: 10 },
  horizontalItem: { width: 180, height: 300, borderRadius: 20, overflow: 'hidden', marginHorizontal: 10 },
  gradient: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 20 },
  image: { width: 140, height: 140, marginBottom: 10, borderRadius: 70 },
  buttonText: { fontSize: 20, fontWeight: 'bold', textAlign: 'center', marginBottom: 5 },
  description: { fontSize: 14, textAlign: 'center', marginHorizontal: 8, marginTop: 5 },
  lightMode: { backgroundColor: '#f8f9fa' },
  lightText: { color: '#333' },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default QuizScreen;
