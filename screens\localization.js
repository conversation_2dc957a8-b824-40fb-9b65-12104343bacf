import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

const resources = {
  en: {
    translation: {
      settings: 'Settings',
      dark_mode: 'Dark Mode',
      language: 'Language',
      language_changed: 'Language changed!',
      app_language_set: 'App language set to',
      restart_to_apply: 'Restart the app to apply changes.',
      // AiChatScreen
      ai_assistant_title: 'Nijeta AI Assistant',
      ask_anything_placeholder: 'Ask me anything about NEET prep!',
      initial_ai_greeting: 'Hello! i am nijeta How can I help you with your NEET preparation today?',
      stop_generating_button: 'Stop Generating',
      typing_indicator: '<PERSON><PERSON><PERSON> is typing...',
      copied_alert: 'Copied',
      message_copied_alert: 'Message copied to clipboard.',
      share_error_alert: 'Share Error',
      could_not_share_alert: 'Could not share the message.',
      clear_chat_alert_title: 'Clear Chat',
      clear_chat_alert_message: 'Are you sure you want to clear the chat history?',
      cancel_button: 'Cancel',
      clear_button: 'Clear',
      clear_error_alert: 'Clear Error',
      failed_clear_history_alert: 'Failed to clear chat history.',
      ai_error_alert: 'AI Error',
      speech_error_alert: 'Speech Error',
      could_not_play_audio_alert: 'Could not play the message audio.',
      generation_stopped_message: 'Generation stopped by user.',
      ai_response_error: "Sorry, I couldn't get a response.", // New key for default AI error
      // Accessibility Labels for AiChatScreen
      translate_button_label: "Change Language",
      clear_chat_button_label: "Clear Chat History",
      speak_message_button_label: "Read Message Aloud",
      copy_message_button_label: "Copy Message Text",
      share_message_button_label: "Share Message Text",
      regenerate_button_label: "Regenerate Last Response",
       stop_generating_button_label: "Stop AI Generation",
        send_button_label: "Send Message",
        mic_button_label: "Start Voice Input", // New key for mic button
        listening_indicator: "Listening...", // New key for voice input indicator
        thumbs_up_button_label: "Give Positive Feedback", // New key for thumbs up
        thumbs_down_button_label: "Give Negative Feedback", // New key for thumbs down

        // QuizQuestions Screen
        loading_questions: 'Loading questions...',
      level: 'Level',
      question_progress: 'Question {{current}} / {{total}}',
      time_left: 'Time Left: {{seconds}}s',
      next_question: 'Next Question',
      finish_quiz: 'Finish Quiz',
      error_title: 'Error',
      score_save_error: 'Could not save your score.',
      no_questions_found: 'No questions found for this category/level.', // New key
      sound_volume: 'Sound Volume' // Key for volume slider
    }
  },
  ta: { // Add Tamil resources
    translation: {
      settings: 'அமைப்புகள்',
      dark_mode: 'இருண்ட பயன்முறை',
      language: 'மொழி',
      language_changed: 'மொழி மாற்றப்பட்டது!',
      app_language_set: 'பயன்பாட்டு மொழி அமைக்கப்பட்டது',
      restart_to_apply: 'மாற்றங்களைப் பயன்படுத்த பயன்பாட்டை மறுதொடக்கம் செய்யவும்.',
      // AiChatScreen - Tamil
      ai_assistant_title: 'நிஜேதா AI உதவியாளர்',
      ask_anything_placeholder: 'நீட் தயாரிப்பு பற்றி என்னிடம் எதுவும் கேளுங்கள்!',
      initial_ai_greeting: 'வணக்கம்! நான் நிஜேதா. உங்கள் நீட் தயாரிப்புக்கு நான் இன்று எப்படி உதவ முடியும்?',
      stop_generating_button: 'உருவாக்குவதை நிறுத்து',
      typing_indicator: 'நிஜேதா தட்டச்சு செய்கிறார்...',
      copied_alert: 'நகலெடுக்கப்பட்டது',
      message_copied_alert: 'செய்தி கிளிப்போர்டுக்கு நகலெடுக்கப்பட்டது.',
      share_error_alert: 'பகிர்வு பிழை',
      could_not_share_alert: 'செய்தியைப் பகிர முடியவில்லை.',
      clear_chat_alert_title: 'அரட்டையை அழிக்கவும்',
      clear_chat_alert_message: 'அரட்டை வரலாற்றை அழிக்க விரும்புகிறீர்களா?',
      cancel_button: 'ரத்துசெய்',
      clear_button: 'அழி',
      clear_error_alert: 'பிழையை அழிக்கவும்',
      failed_clear_history_alert: 'அரட்டை வரலாற்றை அழிக்க முடியவில்லை.',
      ai_error_alert: 'AI பிழை',
      speech_error_alert: 'பேச்சு பிழை',
      could_not_play_audio_alert: 'செய்தி ஆடியோவை இயக்க முடியவில்லை.',
      generation_stopped_message: 'பயனரால் உருவாக்கம் நிறுத்தப்பட்டது.',
      ai_response_error: "மன்னிக்கவும், என்னால் பதிலைப் பெற முடியவில்லை.", // New key for default AI error - Tamil
      // Accessibility Labels for AiChatScreen - Tamil
      translate_button_label: "மொழியை மாற்று",
      clear_chat_button_label: "அரட்டை வரலாற்றை அழி",
      speak_message_button_label: "செய்தியை உரக்கப் படி",
      copy_message_button_label: "செய்தி உரையை நகலெடு",
      share_message_button_label: "செய்தி உரையைப் பகிர்",
      regenerate_button_label: "கடைசி பதிலை மீண்டும் உருவாக்கு",
       stop_generating_button_label: "AI உருவாக்கத்தை நிறுத்து",
        send_button_label: "செய்தியை அனுப்பு",
        mic_button_label: "குரல் உள்ளீட்டைத் தொடங்கு", // New key for mic button - Tamil
        listening_indicator: "கேட்கிறது...", // New key for voice input indicator - Tamil
        thumbs_up_button_label: "நேர்மறை கருத்து தெரிவிக்கவும்", // New key for thumbs up - Tamil
        thumbs_down_button_label: "எதிர்மறை கருத்து தெரிவிக்கவும்", // New key for thumbs down - Tamil

        // QuizQuestions Screen - Tamil (already handled by _ta fields, but keep keys consistent)
        loading_questions: 'கேள்விகள் ஏற்றப்படுகின்றன...',
      level: 'நிலை',
      question_progress: 'கேள்வி {{current}} / {{total}}',
      time_left: 'மீதமுள்ள நேரம்: {{seconds}} வினாடிகள்',
      next_question: 'அடுத்த கேள்வி',
      finish_quiz: 'வினாடியை முடிக்கவும்',
      error_title: 'பிழை',
      score_save_error: 'உங்கள் மதிப்பெண்ணைச் சேமிக்க முடியவில்லை.',
      no_questions_found: 'இந்த வகை/நிலைக்கு கேள்விகள் எதுவும் இல்லை.',
      sound_volume: 'ஒலி அளவு' // Key for volume slider Tamil
    }
  }
};

const loadLanguage = async () => {
  try {
    const storedLang = await AsyncStorage.getItem('appLanguage');
    return storedLang || 'en';
  } catch (error) {
    console.error('Error loading language:', error);
    return 'en';
  }
};

// Function to get supported languages dynamically from resources
const getSupportedLngs = () => Object.keys(resources);

loadLanguage().then((lng) => {
  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng,
      fallbackLng: 'en',
      supportedLngs: getSupportedLngs(), // Add supported languages
      interpolation: { escapeValue: false },
      react: { useSuspense: false }
    })
    .catch((err) => console.error('i18n initialization error:', err));
});

export default i18n;
