# version control
.git
.gitignore

# build artifacts
*.aab
*.apk
*.ipa
build/
android/app/build/
ios/build/
dist/
out/

# node & dependencies
node_modules/
.pnpm/
.pnp/
.yarn/cache/
.yarn/unplugged/

# config/cache
.env*
.cache/
.expo/
.expo-shared/
.idea/
.vscode/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# additional exclusions for smaller upload
android/.gradle/
android/build/
android/app/src/main/assets/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/

# Metro and bundler cache
.metro-health-check*
.metro/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Documentation and media
*.md
docs/
screenshots/
*.mp4
*.mov
*.avi
*.zip
*.tar.gz
*.rar

# Test files
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# Development files
check-ads.js
test-ads.js
*.tmp
*.temp
*.bak
*.backup

# Large PDF files (exclude from build, use remote hosting)
assets/pdfs/
*.pdf

# Large assets (uncomment if you have them)
# assets/dev/
# assets/mockups/
# assets/raw/

# TypeScript cache
*.tsbuildinfo

# Coverage
coverage/
.nyc_output/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
*.swp
*.swo
*~
.eslintcache

# Temporary directories
tmp/
temp/
