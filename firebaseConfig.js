// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { initializeAuth, getReactNativePersistence } from 'firebase/auth'; // Import initializeAuth and persistence
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { getStorage } from "firebase/storage"; // Import getStorage for Firebase Storage
import { getFirestore } from "firebase/firestore"; // Import getFirestore
import { getFunctions } from "firebase/functions"; // Import getFunctions

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyCpYzQOv5Hgz_XtT1Igupl9qdJGjcMYWuY",
  authDomain: "bee-tech-5e03a.firebaseapp.com",
  databaseURL: "https://bee-tech-5e03a-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "bee-tech-5e03a",
  storageBucket: "bee-tech-5e03a.appspot.com", // Corrected storage bucket domain
  messagingSenderId: "189643081632",
  appId: "1:189643081632:web:5dcd41e90221d7914ac6c5"
};

// Initialize Firebase with error handling
let app, auth, storage, db, functions;

try {
  app = initializeApp(firebaseConfig);
  
  // Initialize Firebase Authentication with persistence
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(ReactNativeAsyncStorage)
  });

  // Initialize Firebase Storage
  storage = getStorage(app);

  // Initialize Firestore
  db = getFirestore(app);

  // Initialize Cloud Functions
  functions = getFunctions(app);

  console.log('Firebase initialized successfully');
} catch (error) {
  console.error('Firebase initialization error:', error);
  
  // Provide fallback values
  auth = {
    currentUser: null,
    signIn: () => Promise.reject(new Error('Firebase not initialized')),
    signOut: () => Promise.reject(new Error('Firebase not initialized')),
  };
  
  storage = {
    ref: () => ({ put: () => Promise.reject(new Error('Firebase not initialized')) }),
  };
  
  db = {
    collection: () => ({ add: () => Promise.reject(new Error('Firebase not initialized')) }),
  };
  
  functions = {
    httpsCallable: () => () => Promise.reject(new Error('Firebase not initialized')),
  };
}

export { auth, storage, db, functions }; // Export the auth, storage, db, and functions instances
