// PDF Configuration for Remote Hosting
// Since PDFs are excluded from the build to reduce size, they should be hosted remotely

// Base URL for your PDF hosting using Firebase Storage
const PDF_BASE_URL = __DEV__
  ? 'https://firebasestorage.googleapis.com/v0/b/quiz-bee-techs.appspot.com/o/pdfs%2F' // Development
  : 'https://firebasestorage.googleapis.com/v0/b/quiz-bee-techs.appspot.com/o/pdfs%2F'; // Production

// Firebase Storage URL suffix for public access
const FIREBASE_SUFFIX = '?alt=media';

// PDF file mappings
export const PDF_URLS = {
  // Biology PDFs
  biology: {
    class11: `${PDF_BASE_URL}biology%2FClass_11_Biology.pdf${FIREBASE_SUFFIX}`,
    class12: `${PDF_BASE_URL}biology%2FClass_12_Biology.pdf${FIREBASE_SUFFIX}`,
    chapters12: {
      chapter1: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_1.pdf${FIREBASE_SUFFIX}`,
      chapter2: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_2.pdf${FIREBASE_SUFFIX}`,
      chapter3: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_3.pdf${FIREBASE_SUFFIX}`,
      chapter4: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_4.pdf${FIREBASE_SUFFIX}`,
      chapter5: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_5.pdf${FIREBASE_SUFFIX}`,
      chapter6: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_6.pdf${FIREBASE_SUFFIX}`,
      chapter7: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_7.pdf${FIREBASE_SUFFIX}`,
      chapter8: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_8.pdf${FIREBASE_SUFFIX}`,
      chapter9: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_9.pdf${FIREBASE_SUFFIX}`,
      chapter10: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_10.pdf${FIREBASE_SUFFIX}`,
      chapter11: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_11.pdf${FIREBASE_SUFFIX}`,
      chapter12: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_12.pdf${FIREBASE_SUFFIX}`,
      chapter13: `${PDF_BASE_URL}biology%2Fchapters12%2Fchapters_13.pdf${FIREBASE_SUFFIX}`,
    }
  },

  // Chemistry PDFs
  chemistry: {
    class11Part1: `${PDF_BASE_URL}chemistry%2FClass_11Chemistry_Part_1.pdf${FIREBASE_SUFFIX}`,
    class11Part2: `${PDF_BASE_URL}chemistry%2FClass_11Chemistry_Part_2.pdf${FIREBASE_SUFFIX}`,
    class12Part1: `${PDF_BASE_URL}chemistry%2FClass_12_Chemistry_Part_1.pdf${FIREBASE_SUFFIX}`,
    class12Part2: `${PDF_BASE_URL}chemistry%2FClass_12Chemistry_Part_2.pdf${FIREBASE_SUFFIX}`,
    chapters12: {
      chapter1: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_1.pdf${FIREBASE_SUFFIX}`,
      chapter2: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_2.pdf${FIREBASE_SUFFIX}`,
      chapter3: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_3.pdf${FIREBASE_SUFFIX}`,
      chapter4: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_4.pdf${FIREBASE_SUFFIX}`,
      chapter5: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_5.pdf${FIREBASE_SUFFIX}`,
      chapter6: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_6.pdf${FIREBASE_SUFFIX}`,
      chapter7: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_7.pdf${FIREBASE_SUFFIX}`,
      chapter8: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_8.pdf${FIREBASE_SUFFIX}`,
      chapter9: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_9.pdf${FIREBASE_SUFFIX}`,
      chapter10: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_10.pdf${FIREBASE_SUFFIX}`,
      chapter11: `${PDF_BASE_URL}chemistry%2Fchapters12%2Fchapters_11.pdf${FIREBASE_SUFFIX}`,
    }
  },

  // Physics PDFs
  physics: {
    class11Part1: `${PDF_BASE_URL}physics%2FClass_11_Physics_Part_1.pdf${FIREBASE_SUFFIX}`,
    class11Part2: `${PDF_BASE_URL}physics%2FClass_11Physics_Part_2.pdf${FIREBASE_SUFFIX}`,
    class12Part1: `${PDF_BASE_URL}physics%2FClass_12Physics_Part_1.pdf${FIREBASE_SUFFIX}`,
    class12Part2: `${PDF_BASE_URL}physics%2FClass_12Physics_Part_2.pdf${FIREBASE_SUFFIX}`,
    class12English: `${PDF_BASE_URL}physics%2FPhysics_eng_12th.pdf${FIREBASE_SUFFIX}`,
    chapters12: {
      chapter1: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_1.pdf${FIREBASE_SUFFIX}`,
      chapter2: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_2.pdf${FIREBASE_SUFFIX}`,
      chapter3: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_3.pdf${FIREBASE_SUFFIX}`,
      chapter4: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_4.pdf${FIREBASE_SUFFIX}`,
      chapter5: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_5.pdf${FIREBASE_SUFFIX}`,
      chapter6: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_6.pdf${FIREBASE_SUFFIX}`,
      chapter7: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_7.pdf${FIREBASE_SUFFIX}`,
      chapter8: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_8.pdf${FIREBASE_SUFFIX}`,
      chapter9: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_9.pdf${FIREBASE_SUFFIX}`,
      chapter10: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_10.pdf${FIREBASE_SUFFIX}`,
      chapter11: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_11.pdf${FIREBASE_SUFFIX}`,
      chapter12: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_12.pdf${FIREBASE_SUFFIX}`,
      chapter13: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_13.pdf${FIREBASE_SUFFIX}`,
      chapter14: `${PDF_BASE_URL}physics%2Fchapters12%2Fchapter_14.pdf${FIREBASE_SUFFIX}`,
    }
  }
};

// Helper function to validate and fix PDF URLs
const validateAndFixUrl = (url) => {
  if (!url) return null;

  try {
    // Ensure the URL is properly formatted
    if (url.includes('firebasestorage.googleapis.com')) {
      // Make sure Firebase URLs have the proper suffix
      if (!url.includes('?alt=media')) {
        url += FIREBASE_SUFFIX;
      }
    }

    // Test if URL is valid (using a simple validation instead of URL constructor for React Native compatibility)
    if (url && typeof url === 'string' && (url.startsWith('http://') || url.startsWith('https://'))) {
      return url;
    }
    throw new Error('Invalid URL format');
  } catch (error) {
    console.error('Invalid PDF URL:', url, error);
    return null;
  }
};

// Helper function to get PDF URL
export const getPdfUrl = (subject, type, chapter = null) => {
  try {
    let url;
    if (chapter) {
      url = PDF_URLS[subject]?.[type]?.[`chapter${chapter}`];
    } else {
      url = PDF_URLS[subject]?.[type];
    }

    return validateAndFixUrl(url);
  } catch (error) {
    console.error('Error getting PDF URL:', error);
    return null;
  }
};

// Check if PDF is available
export const isPdfAvailable = (subject, type, chapter = null) => {
  return getPdfUrl(subject, type, chapter) !== null;
};

// Download PDF for offline viewing (optional)
export const downloadPdf = async (url, filename) => {
  try {
    // Implementation depends on your offline storage strategy
    // This is a placeholder for future implementation
    console.log(`Downloading PDF: ${filename} from ${url}`);
    return { success: true, localPath: null };
  } catch (error) {
    console.error('PDF download failed:', error);
    return { success: false, error: error.message };
  }
};
