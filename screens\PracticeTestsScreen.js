import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const PracticeTestsScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();
  const { category = 'NEET' } = route.params || {};

  const [selectedDuration, setSelectedDuration] = useState(15); // minutes
  const [selectedSubject, setSelectedSubject] = useState('Mixed');
  const [testHistory, setTestHistory] = useState([]);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadTestHistory();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const testConfigurations = [
    { duration: 15, questions: 15, label: 'Quick Test' },
    { duration: 30, questions: 30, label: 'Standard Test' },
    { duration: 45, questions: 45, label: 'Extended Test' },
    { duration: 60, questions: 60, label: 'Full Test' },
  ];

  const subjects = ['Mixed', 'Physics', 'Chemistry', 'Biology', 'Mathematics'];

  const loadTestHistory = async () => {
    try {
      const history = await AsyncStorage.getItem(`practice_test_history_${category}`);
      if (history) {
        setTestHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error('Error loading test history:', error);
    }
  };

  const saveTestResult = async (result) => {
    try {
      const newHistory = [result, ...testHistory.slice(0, 9)]; // Keep last 10 tests
      setTestHistory(newHistory);
      await AsyncStorage.setItem(
        `practice_test_history_${category}`,
        JSON.stringify(newHistory)
      );
    } catch (error) {
      console.error('Error saving test result:', error);
    }
  };

  const startPracticeTest = () => {
    const config = testConfigurations.find(c => c.duration === selectedDuration);

    Alert.alert(
      'Start Practice Test',
      `You are about to start a ${config.label} with ${config.questions} questions in ${config.duration} minutes.\n\nSubject: ${selectedSubject}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Test',
          onPress: () => {
            navigation.navigate('TimedQuiz', {
              category: selectedSubject === 'Mixed' ? 'NEET' : selectedSubject,
              duration: selectedDuration * 60, // Convert to seconds
              questionCount: config.questions,
              testType: 'practice',
              onTestComplete: saveTestResult
            });
          }
        }
      ]
    );
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 60) return '#FF9800';
    return '#F44336';
  };

  const renderTestHistory = () => {
    if (testHistory.length === 0) {
      return (
        <View style={styles.emptyHistory}>
          <Ionicons name="document-text-outline" size={48} color="#CCCCCC" />
          <Text style={[
            styles.emptyHistoryText,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            No practice tests completed yet
          </Text>
        </View>
      );
    }

    return testHistory.map((test, index) => (
      <Animated.View
        key={index}
        style={[
          styles.historyItem,
          {
            backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
            opacity: fadeAnim
          }
        ]}
      >
        <View style={styles.historyHeader}>
          <Text style={[
            styles.historyTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            {test.subject} - {test.duration}min Test
          </Text>
          <Text style={[
            styles.historyDate,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 12 * fontSizeMultiplier
            }
          ]}>
            {formatDate(test.timestamp)}
          </Text>
        </View>

        <View style={styles.historyStats}>
          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: getScoreColor(test.percentage),
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {test.percentage}%
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Score
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {test.correct}/{test.total}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Correct
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[
              styles.statValue,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {Math.floor(test.timeSpent / 60)}m
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Time
            </Text>
          </View>
        </View>
      </Animated.View>
    ));
  };

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 24 * fontSizeMultiplier
            }
          ]}>
            Practice Tests
          </Text>
          <Text style={[
            styles.headerSubtitle,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Timed practice sessions for exam preparation
          </Text>
        </View>

        {/* Test Configuration */}
        <View style={styles.configSection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Test Configuration
          </Text>

          {/* Duration Selection */}
          <Text style={[
            styles.configLabel,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 14 * fontSizeMultiplier
            }
          ]}>
            Test Duration
          </Text>
          <View style={styles.optionGrid}>
            {testConfigurations.map((config) => (
              <TouchableOpacity
                key={config.duration}
                style={[
                  styles.optionButton,
                  selectedDuration === config.duration && styles.selectedOption
                ]}
                onPress={() => setSelectedDuration(config.duration)}
              >
                <Text style={[
                  styles.optionText,
                  { fontSize: 14 * fontSizeMultiplier },
                  selectedDuration === config.duration && styles.selectedOptionText
                ]}>
                  {config.label}
                </Text>
                <Text style={[
                  styles.optionSubtext,
                  { fontSize: 12 * fontSizeMultiplier },
                  selectedDuration === config.duration && styles.selectedOptionText
                ]}>
                  {config.questions} questions
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Subject Selection */}
          <Text style={[
            styles.configLabel,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 14 * fontSizeMultiplier
            }
          ]}>
            Subject Focus
          </Text>
          <View style={styles.subjectGrid}>
            {subjects.map((subject) => (
              <TouchableOpacity
                key={subject}
                style={[
                  styles.subjectButton,
                  selectedSubject === subject && styles.selectedSubject
                ]}
                onPress={() => setSelectedSubject(subject)}
              >
                <Text style={[
                  styles.subjectText,
                  { fontSize: 14 * fontSizeMultiplier },
                  selectedSubject === subject && styles.selectedSubjectText
                ]}>
                  {subject}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Start Test Button */}
          <TouchableOpacity style={styles.startButton} onPress={startPracticeTest}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.startButtonGradient}
            >
              <Ionicons name="play" size={24} color="#FFFFFF" />
              <Text style={[
                styles.startButtonText,
                { fontSize: 18 * fontSizeMultiplier }
              ]}>
                Start Practice Test
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Test History */}
        <View style={styles.historySection}>
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            Recent Tests
          </Text>
          {renderTestHistory()}
        </View>
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    lineHeight: 22,
  },
  configSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  configLabel: {
    fontWeight: '500',
    marginBottom: 12,
    marginTop: 16,
  },
  optionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionButton: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#E0E0E0',
    marginBottom: 12,
    alignItems: 'center',
  },
  selectedOption: {
    backgroundColor: '#667eea',
  },
  optionText: {
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  optionSubtext: {
    color: '#666666',
  },
  selectedOptionText: {
    color: '#FFFFFF',
  },
  subjectGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  subjectButton: {
    width: '30%',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#E0E0E0',
    marginBottom: 8,
    alignItems: 'center',
  },
  selectedSubject: {
    backgroundColor: '#f093fb',
  },
  subjectText: {
    fontWeight: '500',
    color: '#333333',
  },
  selectedSubjectText: {
    color: '#FFFFFF',
  },
  startButton: {
    marginTop: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  startButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  historySection: {
    paddingHorizontal: 20,
  },
  historyItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyTitle: {
    fontWeight: 'bold',
    flex: 1,
  },
  historyDate: {
    fontStyle: 'italic',
  },
  historyStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyHistoryText: {
    marginTop: 16,
    textAlign: 'center',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default PracticeTestsScreen;
