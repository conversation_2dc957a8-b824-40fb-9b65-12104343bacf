{"cli": {"version": ">= 15.0.15", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal"}, "production": {"autoIncrement": true, "node": "20.18.0", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "env": {"NODE_ENV": "production"}}, "release": {"autoIncrement": true, "node": "20.18.0", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "env": {"NODE_ENV": "production"}}, "local-apk": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {}}}