import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Animated, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute } from '@react-navigation/native';
import * as Clipboard from 'expo-clipboard';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const FormulaSheetsScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const { category = 'Physics' } = route.params || {};

  const [selectedSubject, setSelectedSubject] = useState(category);
  const [expandedSections, setExpandedSections] = useState(new Set());
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const formulaData = {
    Physics: {
      Mechanics: [
        { name: 'Velocity', formula: 'v = u + at', description: 'Final velocity = Initial velocity + acceleration × time' },
        { name: 'Displacement', formula: 's = ut + ½at²', description: 'Displacement with constant acceleration' },
        { name: 'Newton\'s Second Law', formula: 'F = ma', description: 'Force = mass × acceleration' },
        { name: 'Kinetic Energy', formula: 'KE = ½mv²', description: 'Kinetic energy of a moving object' },
        { name: 'Potential Energy', formula: 'PE = mgh', description: 'Gravitational potential energy' },
        { name: 'Work', formula: 'W = F·s·cosθ', description: 'Work done by a force' },
        { name: 'Power', formula: 'P = W/t = F·v', description: 'Rate of doing work' },
      ],
      Thermodynamics: [
        { name: 'First Law', formula: 'ΔU = Q - W', description: 'Change in internal energy' },
        { name: 'Ideal Gas Law', formula: 'PV = nRT', description: 'Equation of state for ideal gas' },
        { name: 'Efficiency', formula: 'η = W/Q₁ = 1 - Q₂/Q₁', description: 'Efficiency of heat engine' },
        { name: 'Heat Capacity', formula: 'C = Q/ΔT', description: 'Heat capacity of a substance' },
      ],
      Electricity: [
        { name: 'Ohm\'s Law', formula: 'V = IR', description: 'Voltage = Current × Resistance' },
        { name: 'Power', formula: 'P = VI = I²R = V²/R', description: 'Electrical power' },
        { name: 'Capacitance', formula: 'C = Q/V', description: 'Capacitance of a capacitor' },
        { name: 'Energy in Capacitor', formula: 'U = ½CV²', description: 'Energy stored in capacitor' },
      ],
      Waves: [
        { name: 'Wave Equation', formula: 'v = fλ', description: 'Wave speed = frequency × wavelength' },
        { name: 'Doppler Effect', formula: 'f\' = f(v±v₀)/(v±vₛ)', description: 'Frequency shift due to motion' },
        { name: 'Interference', formula: 'Δφ = 2πΔx/λ', description: 'Phase difference in interference' },
      ]
    },
    Chemistry: {
      'Atomic Structure': [
        { name: 'Rydberg Formula', formula: '1/λ = R(1/n₁² - 1/n₂²)', description: 'Hydrogen spectrum wavelengths' },
        { name: 'De Broglie Wavelength', formula: 'λ = h/mv', description: 'Matter wave wavelength' },
        { name: 'Heisenberg Principle', formula: 'Δx·Δp ≥ h/4π', description: 'Uncertainty principle' },
      ],
      Thermochemistry: [
        { name: 'Enthalpy Change', formula: 'ΔH = H₂ - H₁', description: 'Change in enthalpy' },
        { name: 'Gibbs Free Energy', formula: 'ΔG = ΔH - TΔS', description: 'Free energy change' },
        { name: 'Equilibrium Constant', formula: 'ΔG° = -RT ln K', description: 'Relation between ΔG° and K' },
      ],
      'Chemical Kinetics': [
        { name: 'Rate Law', formula: 'Rate = k[A]ᵐ[B]ⁿ', description: 'Rate of chemical reaction' },
        { name: 'Arrhenius Equation', formula: 'k = Ae^(-Ea/RT)', description: 'Temperature dependence of rate constant' },
        { name: 'Half-life (1st order)', formula: 't₁/₂ = 0.693/k', description: 'Half-life for first-order reaction' },
      ],
      Solutions: [
        { name: 'Molarity', formula: 'M = n/V', description: 'Moles of solute per liter of solution' },
        { name: 'Molality', formula: 'm = n/kg', description: 'Moles of solute per kg of solvent' },
        { name: 'Raoult\'s Law', formula: 'P = X·P°', description: 'Vapor pressure of solution' },
      ]
    },
    Biology: {
      Genetics: [
        { name: 'Hardy-Weinberg', formula: 'p² + 2pq + q² = 1', description: 'Allele frequency in population' },
        { name: 'Gene Frequency', formula: 'p + q = 1', description: 'Sum of allele frequencies' },
        { name: 'Mutation Rate', formula: 'μ = m/N', description: 'Rate of mutation occurrence' },
      ],
      Ecology: [
        { name: 'Population Growth', formula: 'dN/dt = rN', description: 'Exponential population growth' },
        { name: 'Carrying Capacity', formula: 'dN/dt = rN(1-N/K)', description: 'Logistic growth model' },
        { name: 'Species Diversity', formula: 'H = -Σ(pi ln pi)', description: 'Shannon diversity index' },
      ],
      Physiology: [
        { name: 'Cardiac Output', formula: 'CO = HR × SV', description: 'Heart rate × stroke volume' },
        { name: 'BMI', formula: 'BMI = weight/height²', description: 'Body mass index' },
        { name: 'Surface Area', formula: 'SA = √(hw/3600)', description: 'Body surface area (DuBois formula)' },
      ]
    }
  };

  const subjects = ['Physics', 'Chemistry', 'Biology'];

  const toggleSection = (section) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const copyFormula = async (formula, name) => {
    try {
      await Clipboard.setStringAsync(formula);
      Alert.alert('Copied!', `${name} formula copied to clipboard`);
    } catch (error) {
      Alert.alert('Error', 'Failed to copy formula');
    }
  };

  const renderFormula = (item, index) => (
    <Animated.View
      key={index}
      style={[
        styles.formulaItem,
        { opacity: fadeAnim }
      ]}
    >
      <View style={styles.formulaHeader}>
        <Text style={[
          styles.formulaName,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          {item.name}
        </Text>
        <TouchableOpacity
          onPress={() => copyFormula(item.formula, item.name)}
          style={styles.copyButton}
        >
          <Ionicons name="copy-outline" size={20} color="#667eea" />
        </TouchableOpacity>
      </View>

      <View style={styles.formulaBox}>
        <Text style={[
          styles.formulaText,
          { fontSize: 18 * fontSizeMultiplier }
        ]}>
          {item.formula}
        </Text>
      </View>

      <Text style={[
        styles.formulaDescription,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        {item.description}
      </Text>
    </Animated.View>
  );

  const renderSection = (sectionName, formulas) => {
    const isExpanded = expandedSections.has(sectionName);

    return (
      <View key={sectionName} style={styles.section}>
        <TouchableOpacity
          style={[
            styles.sectionHeader,
            { backgroundColor: darkMode ? '#2A2A2A' : '#F0F0F0' }
          ]}
          onPress={() => toggleSection(sectionName)}
        >
          <Text style={[
            styles.sectionTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 18 * fontSizeMultiplier
            }
          ]}>
            {sectionName}
          </Text>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={24}
            color={darkMode ? '#FFFFFF' : '#333333'}
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {formulas.map((formula, index) => renderFormula(formula, index))}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.headerTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 24 * fontSizeMultiplier
          }
        ]}>
          Formula Sheets
        </Text>
      </View>

      {/* Subject Selector */}
      <View style={styles.subjectSelector}>
        {subjects.map((subject) => (
          <TouchableOpacity
            key={subject}
            style={[
              styles.subjectButton,
              selectedSubject === subject && styles.activeSubjectButton
            ]}
            onPress={() => setSelectedSubject(subject)}
          >
            <Text style={[
              styles.subjectButtonText,
              { fontSize: 14 * fontSizeMultiplier },
              selectedSubject === subject && styles.activeSubjectButtonText
            ]}>
              {subject}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Formulas */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {Object.entries(formulaData[selectedSubject] || {}).map(([sectionName, formulas]) =>
          renderSection(sectionName, formulas)
        )}
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
  },
  subjectSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  subjectButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  activeSubjectButton: {
    backgroundColor: '#667eea',
  },
  subjectButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  activeSubjectButtonText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  sectionTitle: {
    fontWeight: 'bold',
  },
  sectionContent: {
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
  },
  formulaItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  formulaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  formulaName: {
    fontWeight: 'bold',
    flex: 1,
  },
  copyButton: {
    padding: 4,
  },
  formulaBox: {
    backgroundColor: '#667eea',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  formulaText: {
    color: '#FFFFFF',
    fontFamily: 'monospace',
    fontWeight: 'bold',
  },
  formulaDescription: {
    fontStyle: 'italic',
    lineHeight: 20,
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default FormulaSheetsScreen;
