import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Animated, TextInput, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const StudyPlaylistsScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();

  const [playlists, setPlaylists] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadPlaylists();
    handleRouteParams();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleRouteParams = () => {
    if (route.params?.action === 'add_question' && route.params?.question) {
      // Show playlist selection for adding question
      showAddToPlaylistModal(route.params.question);
    }
  };

  const loadPlaylists = async () => {
    try {
      const playlistsData = await AsyncStorage.getItem('user_playlists');
      if (playlistsData) {
        setPlaylists(JSON.parse(playlistsData));
      }
    } catch (error) {
      console.error('Error loading playlists:', error);
    } finally {
      setLoading(false);
    }
  };

  const createPlaylist = async () => {
    if (!newPlaylistName.trim()) {
      Alert.alert('Error', 'Please enter a playlist name');
      return;
    }

    const newPlaylist = {
      id: Date.now().toString(),
      name: newPlaylistName.trim(),
      description: newPlaylistDescription.trim(),
      questions: [],
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      color: getRandomColor()
    };

    try {
      const updatedPlaylists = [...playlists, newPlaylist];
      setPlaylists(updatedPlaylists);
      await AsyncStorage.setItem('user_playlists', JSON.stringify(updatedPlaylists));

      setShowCreateModal(false);
      setNewPlaylistName('');
      setNewPlaylistDescription('');

      Alert.alert('Success', 'Playlist created successfully!');
    } catch (error) {
      console.error('Error creating playlist:', error);
      Alert.alert('Error', 'Failed to create playlist');
    }
  };

  const deletePlaylist = async (playlistId) => {
    Alert.alert(
      'Delete Playlist',
      'Are you sure you want to delete this playlist? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const updatedPlaylists = playlists.filter(p => p.id !== playlistId);
              setPlaylists(updatedPlaylists);
              await AsyncStorage.setItem('user_playlists', JSON.stringify(updatedPlaylists));
            } catch (error) {
              console.error('Error deleting playlist:', error);
              Alert.alert('Error', 'Failed to delete playlist');
            }
          }
        }
      ]
    );
  };

  const showAddToPlaylistModal = (question) => {
    if (playlists.length === 0) {
      Alert.alert(
        'No Playlists',
        'You need to create a playlist first. Would you like to create one now?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Create Playlist', onPress: () => setShowCreateModal(true) }
        ]
      );
      return;
    }

    const playlistOptions = playlists.map(playlist => ({
      text: `${playlist.name} (${playlist.questions.length} questions)`,
      onPress: () => addQuestionToPlaylist(question, playlist.id)
    }));

    Alert.alert(
      'Add to Playlist',
      'Select a playlist to add this question:',
      [
        ...playlistOptions,
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const addQuestionToPlaylist = async (question, playlistId) => {
    try {
      const updatedPlaylists = playlists.map(playlist => {
        if (playlist.id === playlistId) {
          // Check if question already exists
          const questionExists = playlist.questions.some(q => q.id === question.id);
          if (questionExists) {
            Alert.alert('Info', 'This question is already in the playlist');
            return playlist;
          }

          return {
            ...playlist,
            questions: [...playlist.questions, { ...question, addedAt: new Date().toISOString() }],
            lastModified: new Date().toISOString()
          };
        }
        return playlist;
      });

      setPlaylists(updatedPlaylists);
      await AsyncStorage.setItem('user_playlists', JSON.stringify(updatedPlaylists));
      Alert.alert('Success', 'Question added to playlist!');
    } catch (error) {
      console.error('Error adding question to playlist:', error);
      Alert.alert('Error', 'Failed to add question to playlist');
    }
  };

  const getRandomColor = () => {
    const colors = [
      ['#667eea', '#764ba2'],
      ['#f093fb', '#f5576c'],
      ['#4ECDC4', '#44A08D'],
      ['#FFA726', '#FF7043'],
      ['#AB47BC', '#8E24AA'],
      ['#FF6B6B', '#FF8E8E']
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const openPlaylist = (playlist) => {
    navigation.navigate('PlaylistDetails', { playlist });
  };

  const startPlaylistQuiz = (playlist) => {
    if (playlist.questions.length === 0) {
      Alert.alert('Empty Playlist', 'This playlist has no questions yet. Add some questions first.');
      return;
    }

    navigation.navigate('QuizQuestions', {
      questions: playlist.questions,
      category: 'Custom Playlist',
      playlistName: playlist.name,
      mode: 'playlist'
    });
  };

  const renderPlaylistCard = (playlist, index) => (
    <Animated.View
      key={playlist.id}
      style={[
        styles.playlistCard,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0]
            })
          }]
        }
      ]}
    >
      <TouchableOpacity
        onPress={() => openPlaylist(playlist)}
        style={styles.playlistButton}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={playlist.color}
          style={styles.playlistGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.playlistHeader}>
            <View style={styles.playlistTitleContainer}>
              <Text style={[
                styles.playlistTitle,
                { fontSize: 18 * fontSizeMultiplier }
              ]}>
                {playlist.name}
              </Text>
              <Text style={[
                styles.questionCount,
                { fontSize: 12 * fontSizeMultiplier }
              ]}>
                {playlist.questions.length} question{playlist.questions.length !== 1 ? 's' : ''}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => deletePlaylist(playlist.id)}
            >
              <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {playlist.description ? (
            <Text style={[
              styles.playlistDescription,
              { fontSize: 14 * fontSizeMultiplier }
            ]}>
              {playlist.description}
            </Text>
          ) : null}

          <View style={styles.playlistFooter}>
            <Text style={[
              styles.dateText,
              { fontSize: 10 * fontSizeMultiplier }
            ]}>
              Created: {new Date(playlist.createdAt).toLocaleDateString()}
            </Text>

            <TouchableOpacity
              style={styles.playButton}
              onPress={() => startPlaylistQuiz(playlist)}
            >
              <Ionicons name="play" size={16} color="#FFFFFF" />
              <Text style={[
                styles.playButtonText,
                { fontSize: 12 * fontSizeMultiplier }
              ]}>
                Start Quiz
              </Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="library-outline" size={64} color="#CCCCCC" />
      <Text style={[
        styles.emptyStateTitle,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 18 * fontSizeMultiplier
        }
      ]}>
        No Playlists Yet
      </Text>
      <Text style={[
        styles.emptyStateText,
        {
          color: darkMode ? '#CCCCCC' : '#666666',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        Create custom study playlists to organize your favorite questions
      </Text>

      <TouchableOpacity
        style={styles.createFirstButton}
        onPress={() => setShowCreateModal(true)}
      >
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.createFirstGradient}
        >
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={[
            styles.createFirstText,
            { fontSize: 14 * fontSizeMultiplier }
          ]}>
            Create Your First Playlist
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.loadingText, { color: darkMode ? '#FFFFFF' : '#333333' }]}>
          Loading playlists...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[
          styles.headerTitle,
          {
            color: darkMode ? '#FFFFFF' : '#333333',
            fontSize: 24 * fontSizeMultiplier
          }
        ]}>
          Study Playlists
        </Text>
        <Text style={[
          styles.headerSubtitle,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 16 * fontSizeMultiplier
          }
        ]}>
          Organize your study materials
        </Text>
      </View>

      {/* Create Playlist Button */}
      {playlists.length > 0 && (
        <View style={styles.createButtonContainer}>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => setShowCreateModal(true)}
          >
            <LinearGradient
              colors={['#f093fb', '#f5576c']}
              style={styles.createButtonGradient}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
              <Text style={[
                styles.createButtonText,
                { fontSize: 14 * fontSizeMultiplier }
              ]}>
                Create New Playlist
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}

      {/* Playlists List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {playlists.length === 0 ? (
          renderEmptyState()
        ) : (
          playlists.map((playlist, index) => renderPlaylistCard(playlist, index))
        )}
      </ScrollView>

      {/* Create Playlist Modal */}
      <Modal
        visible={showCreateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[
            styles.modalContent,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.modalTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              Create New Playlist
            </Text>

            <TextInput
              style={[
                styles.modalInput,
                {
                  backgroundColor: darkMode ? '#3A3A3A' : '#F0F0F0',
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}
              placeholder="Playlist name"
              placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
              value={newPlaylistName}
              onChangeText={setNewPlaylistName}
              maxLength={50}
            />

            <TextInput
              style={[
                styles.modalInput,
                styles.modalTextArea,
                {
                  backgroundColor: darkMode ? '#3A3A3A' : '#F0F0F0',
                  color: darkMode ? '#FFFFFF' : '#333333',
                  fontSize: 14 * fontSizeMultiplier
                }
              ]}
              placeholder="Description (optional)"
              placeholderTextColor={darkMode ? '#AAAAAA' : '#999999'}
              value={newPlaylistDescription}
              onChangeText={setNewPlaylistDescription}
              multiline={true}
              numberOfLines={3}
              maxLength={200}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowCreateModal(false);
                  setNewPlaylistName('');
                  setNewPlaylistDescription('');
                }}
              >
                <Text style={[
                  styles.cancelButtonText,
                  { fontSize: 14 * fontSizeMultiplier }
                ]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createModalButton]}
                onPress={createPlaylist}
              >
                <Text style={[
                  styles.createModalButtonText,
                  { fontSize: 14 * fontSizeMultiplier }
                ]}>
                  Create
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
          fallbackToWebView={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
  },
  createButtonContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  createButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  createButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  playlistCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  playlistButton: {
    width: '100%',
  },
  playlistGradient: {
    padding: 20,
  },
  playlistHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  playlistTitleContainer: {
    flex: 1,
  },
  playlistTitle: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  questionCount: {
    color: '#FFFFFF',
    opacity: 0.8,
  },
  deleteButton: {
    padding: 4,
  },
  playlistDescription: {
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
    lineHeight: 20,
  },
  playlistFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    color: '#FFFFFF',
    opacity: 0.7,
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  playButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  createFirstButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  createFirstGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createFirstText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
  },
  modalTitle: {
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  modalTextArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#E0E0E0',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666666',
    fontWeight: '500',
  },
  createModalButton: {
    backgroundColor: '#667eea',
    marginLeft: 8,
  },
  createModalButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default StudyPlaylistsScreen;
