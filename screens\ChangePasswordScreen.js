import React, { useState, useContext } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { auth } from '../firebaseConfig';
import { EmailAuthProvider, reauthenticateWithCredential, updatePassword } from 'firebase/auth';

import { Ionicons } from '@expo/vector-icons';

const ChangePasswordScreen = ({ navigation }) => {
  const darkMode = false;
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handlePasswordChange = async () => {
    setError(''); // Clear previous errors
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError('Please fill in all fields.');
      return;
    }
    if (newPassword !== confirmPassword) {
      setError('New passwords do not match.');
      return;
    }
    if (newPassword.length < 6) {
      setError('New password must be at least 6 characters long.');
      return;
    }

    setLoading(true);
    const user = auth.currentUser;

    if (!user || !user.email) {
      setError('User not found or email missing.');
      setLoading(false);
      return;
    }

    // Re-authenticate the user first
    const credential = EmailAuthProvider.credential(user.email, currentPassword);

    try {
      await reauthenticateWithCredential(user, credential);
      // User re-authenticated successfully, now update the password
      await updatePassword(user, newPassword);
      setLoading(false);
      Alert.alert('Success', 'Password updated successfully.', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
      // Clear fields after success
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (reauthError) {
      setLoading(false);
      console.error("Re-authentication/Password Update Error:", reauthError);
      if (reauthError.code === 'auth/wrong-password') {
        setError('Incorrect current password.');
      } else if (reauthError.code === 'auth/too-many-requests') {
        setError('Too many attempts. Please try again later.');
      } else {
        setError('Failed to update password. Please try again.');
      }
    }
  };

  // Dynamic styles based on theme
  const dynamicStyles = styles(darkMode);

  return (
    <View style={dynamicStyles.container}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={dynamicStyles.backButton}>
        <Ionicons name="arrow-back" size={24} color={darkMode ? '#fff' : '#000'} />
      </TouchableOpacity>
      <Text style={dynamicStyles.title}>Change Password</Text>

      {error ? <Text style={dynamicStyles.errorText}>{error}</Text> : null}

      <TextInput
        style={dynamicStyles.input}
        placeholder="Current Password"
        placeholderTextColor={darkMode ? '#aaa' : '#999'}
        secureTextEntry
        value={currentPassword}
        onChangeText={setCurrentPassword}
        autoCapitalize="none"
      />
      <TextInput
        style={dynamicStyles.input}
        placeholder="New Password"
        placeholderTextColor={darkMode ? '#aaa' : '#999'}
        secureTextEntry
        value={newPassword}
        onChangeText={setNewPassword}
        autoCapitalize="none"
      />
      <TextInput
        style={dynamicStyles.input}
        placeholder="Confirm New Password"
        placeholderTextColor={darkMode ? '#aaa' : '#999'}
        secureTextEntry
        value={confirmPassword}
        onChangeText={setConfirmPassword}
        autoCapitalize="none"
      />

      <TouchableOpacity
        style={[dynamicStyles.button, loading && dynamicStyles.buttonDisabled]}
        onPress={handlePasswordChange}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={dynamicStyles.buttonText}>Update Password</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

// Styles function to adapt to dark mode
const styles = (darkMode) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: darkMode ? '#000' : '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center', // Center content vertically
  },
  backButton: {
    position: 'absolute',
    top: 40, // Adjust as needed for status bar height
    left: 20,
    zIndex: 1, // Ensure it's above other elements
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: darkMode ? '#fff' : '#000',
    marginBottom: 30,
    textAlign: 'center',
  },
  input: {
    width: '100%',
    height: 50,
    backgroundColor: darkMode ? '#333' : '#fff',
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
    color: darkMode ? '#fff' : '#000',
    borderWidth: 1,
    borderColor: darkMode ? '#555' : '#ccc',
  },
  button: {
    width: '100%',
    height: 50,
    backgroundColor: '#007bff',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: '#a0cfff', // Lighter blue when disabled
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorText: {
    color: 'red',
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 14,
  },
});

export default ChangePasswordScreen;
