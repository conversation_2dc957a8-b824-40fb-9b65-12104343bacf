import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BannerAdComponent from '../components/BannerAdComponent';
import { AD_PLACEMENTS } from '../src/config/adConfig';

const SpacedRepetitionScreen = () => {
  const darkMode = false;
  const fontSizeMultiplier = 1;
  const route = useRoute();
  const navigation = useNavigation();
  const { category = 'NEET' } = route.params || {};

  const [reviewItems, setReviewItems] = useState([]);
  const [todayReviews, setTodayReviews] = useState([]);
  const [upcomingReviews, setUpcomingReviews] = useState([]);
  const [stats, setStats] = useState({ total: 0, mastered: 0, learning: 0, new: 0 });
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadSpacedRepetitionData();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const spacedRepetitionIntervals = [1, 3, 7, 14, 30, 90]; // days

  const loadSpacedRepetitionData = async () => {
    try {
      const data = await AsyncStorage.getItem(`spaced_repetition_${category}`);
      const items = data ? JSON.parse(data) : [];

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayItems = [];
      const upcomingItems = [];
      let totalItems = items.length;
      let masteredItems = 0;
      let learningItems = 0;
      let newItems = 0;

      items.forEach(item => {
        const reviewDate = new Date(item.nextReview);
        reviewDate.setHours(0, 0, 0, 0);

        if (reviewDate <= today) {
          todayItems.push(item);
        } else {
          upcomingItems.push(item);
        }

        // Calculate stats
        if (item.interval >= 30) {
          masteredItems++;
        } else if (item.interval > 1) {
          learningItems++;
        } else {
          newItems++;
        }
      });

      setReviewItems(items);
      setTodayReviews(todayItems.sort((a, b) => new Date(a.nextReview) - new Date(b.nextReview)));
      setUpcomingReviews(upcomingItems.sort((a, b) => new Date(a.nextReview) - new Date(b.nextReview)));
      setStats({ total: totalItems, mastered: masteredItems, learning: learningItems, new: newItems });

      // If no items exist, create some sample items
      if (items.length === 0) {
        await createSampleReviewItems();
      }
    } catch (error) {
      console.error('Error loading spaced repetition data:', error);
    }
  };

  const createSampleReviewItems = async () => {
    const sampleItems = [
      {
        id: '1',
        question: 'What is the powerhouse of the cell?',
        answer: 'Mitochondria',
        subject: 'Biology',
        difficulty: 'easy',
        interval: 1,
        nextReview: new Date().toISOString(),
        easeFactor: 2.5,
        repetitions: 0,
        lastReviewed: null
      },
      {
        id: '2',
        question: 'What is Newton\'s First Law of Motion?',
        answer: 'An object at rest stays at rest and an object in motion stays in motion unless acted upon by an external force',
        subject: 'Physics',
        difficulty: 'medium',
        interval: 1,
        nextReview: new Date().toISOString(),
        easeFactor: 2.5,
        repetitions: 0,
        lastReviewed: null
      },
      {
        id: '3',
        question: 'What is the chemical formula for water?',
        answer: 'H₂O',
        subject: 'Chemistry',
        difficulty: 'easy',
        interval: 3,
        nextReview: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        easeFactor: 2.5,
        repetitions: 1,
        lastReviewed: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    await AsyncStorage.setItem(`spaced_repetition_${category}`, JSON.stringify(sampleItems));
    loadSpacedRepetitionData();
  };

  const calculateNextReview = (item, quality) => {
    // SM-2 Algorithm implementation
    let { interval, easeFactor, repetitions } = item;

    if (quality >= 3) {
      if (repetitions === 0) {
        interval = 1;
      } else if (repetitions === 1) {
        interval = 6;
      } else {
        interval = Math.round(interval * easeFactor);
      }
      repetitions++;
    } else {
      repetitions = 0;
      interval = 1;
    }

    easeFactor = easeFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
    if (easeFactor < 1.3) easeFactor = 1.3;

    const nextReview = new Date();
    nextReview.setDate(nextReview.getDate() + interval);

    return {
      ...item,
      interval,
      easeFactor,
      repetitions,
      nextReview: nextReview.toISOString(),
      lastReviewed: new Date().toISOString()
    };
  };

  const handleReviewResponse = async (item, quality) => {
    try {
      const updatedItem = calculateNextReview(item, quality);
      const updatedItems = reviewItems.map(i => i.id === item.id ? updatedItem : i);

      await AsyncStorage.setItem(`spaced_repetition_${category}`, JSON.stringify(updatedItems));
      loadSpacedRepetitionData();

      const qualityText = ['Again', 'Hard', 'Good', 'Easy'][quality];
      Alert.alert(
        'Review Complete',
        `Next review in ${updatedItem.interval} day${updatedItem.interval > 1 ? 's' : ''}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error updating review item:', error);
    }
  };

  const startReviewSession = () => {
    if (todayReviews.length === 0) {
      Alert.alert('No Reviews', 'You have no items to review today. Great job!');
      return;
    }

    // For now, use a simple alert to show review items
    // TODO: Create a dedicated SpacedRepetitionReview screen
    Alert.alert(
      'Review Session',
      `You have ${todayReviews.length} items to review today. This feature will be enhanced in a future update.`,
      [
        { text: 'OK', style: 'default' },
        {
          text: 'Practice Quiz Instead',
          onPress: () => {
            navigation.navigate('QuizQuestions', {
              category: category,
              level: 1,
              mode: 'spaced_repetition'
            });
          }
        }
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays > 0) return `In ${diffDays} days`;
    return `${Math.abs(diffDays)} days ago`;
  };

  const getSubjectColor = (subject) => {
    const colors = {
      Physics: '#667eea',
      Chemistry: '#f093fb',
      Biology: '#4ECDC4',
      Mathematics: '#FFA726',
      default: '#AB47BC'
    };
    return colors[subject] || colors.default;
  };

  const renderTodayReview = (item, index) => (
    <Animated.View
      key={item.id}
      style={[
        styles.reviewItem,
        {
          backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF',
          opacity: fadeAnim
        }
      ]}
    >
      <View style={styles.reviewHeader}>
        <View style={[
          styles.subjectBadge,
          { backgroundColor: getSubjectColor(item.subject) }
        ]}>
          <Text style={[
            styles.subjectText,
            { fontSize: 10 * fontSizeMultiplier }
          ]}>
            {item.subject}
          </Text>
        </View>
        <Text style={[
          styles.intervalText,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 12 * fontSizeMultiplier
          }
        ]}>
          Interval: {item.interval} day{item.interval > 1 ? 's' : ''}
        </Text>
      </View>

      <Text style={[
        styles.questionText,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        {item.question}
      </Text>

      <View style={styles.reviewStats}>
        <Text style={[
          styles.statsText,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 12 * fontSizeMultiplier
          }
        ]}>
          Repetitions: {item.repetitions} • Ease: {item.easeFactor.toFixed(1)}
        </Text>
      </View>
    </Animated.View>
  );

  const renderUpcomingReview = (item, index) => (
    <View
      key={item.id}
      style={[
        styles.upcomingItem,
        { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
      ]}
    >
      <View style={styles.upcomingHeader}>
        <View style={[
          styles.subjectBadge,
          { backgroundColor: getSubjectColor(item.subject) }
        ]}>
          <Text style={[
            styles.subjectText,
            { fontSize: 10 * fontSizeMultiplier }
          ]}>
            {item.subject}
          </Text>
        </View>
        <Text style={[
          styles.dateText,
          {
            color: darkMode ? '#CCCCCC' : '#666666',
            fontSize: 12 * fontSizeMultiplier
          }
        ]}>
          {formatDate(item.nextReview)}
        </Text>
      </View>

      <Text style={[
        styles.upcomingQuestion,
        {
          color: darkMode ? '#FFFFFF' : '#333333',
          fontSize: 14 * fontSizeMultiplier
        }
      ]}>
        {item.question}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, darkMode ? styles.darkContainer : styles.lightContainer]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[
            styles.headerTitle,
            {
              color: darkMode ? '#FFFFFF' : '#333333',
              fontSize: 24 * fontSizeMultiplier
            }
          ]}>
            Spaced Repetition
          </Text>
          <Text style={[
            styles.headerSubtitle,
            {
              color: darkMode ? '#CCCCCC' : '#666666',
              fontSize: 16 * fontSizeMultiplier
            }
          ]}>
            Smart revision scheduling for long-term retention
          </Text>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={[
            styles.statCard,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.statValue,
              {
                color: '#4CAF50',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {stats.mastered}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Mastered
            </Text>
          </View>

          <View style={[
            styles.statCard,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.statValue,
              {
                color: '#FF9800',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {stats.learning}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Learning
            </Text>
          </View>

          <View style={[
            styles.statCard,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.statValue,
              {
                color: '#2196F3',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {stats.new}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              New
            </Text>
          </View>

          <View style={[
            styles.statCard,
            { backgroundColor: darkMode ? '#2A2A2A' : '#FFFFFF' }
          ]}>
            <Text style={[
              styles.statValue,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 20 * fontSizeMultiplier
              }
            ]}>
              {stats.total}
            </Text>
            <Text style={[
              styles.statLabel,
              {
                color: darkMode ? '#CCCCCC' : '#666666',
                fontSize: 12 * fontSizeMultiplier
              }
            ]}>
              Total
            </Text>
          </View>
        </View>

        {/* Today's Reviews */}
        <View style={styles.todaySection}>
          <View style={styles.sectionHeader}>
            <Text style={[
              styles.sectionTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 18 * fontSizeMultiplier
              }
            ]}>
              Today's Reviews ({todayReviews.length})
            </Text>

            {todayReviews.length > 0 && (
              <TouchableOpacity style={styles.startButton} onPress={startReviewSession}>
                <LinearGradient
                  colors={['#667eea', '#764ba2']}
                  style={styles.startButtonGradient}
                >
                  <Ionicons name="play" size={16} color="#FFFFFF" />
                  <Text style={[
                    styles.startButtonText,
                    { fontSize: 14 * fontSizeMultiplier }
                  ]}>
                    Start Review
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            )}
          </View>

          {todayReviews.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="checkmark-circle-outline" size={48} color="#4CAF50" />
              <Text style={[
                styles.emptyStateText,
                {
                  color: darkMode ? '#CCCCCC' : '#666666',
                  fontSize: 16 * fontSizeMultiplier
                }
              ]}>
                All caught up! No reviews due today.
              </Text>
            </View>
          ) : (
            todayReviews.slice(0, 3).map((item, index) => renderTodayReview(item, index))
          )}
        </View>

        {/* Upcoming Reviews */}
        {upcomingReviews.length > 0 && (
          <View style={styles.upcomingSection}>
            <Text style={[
              styles.sectionTitle,
              {
                color: darkMode ? '#FFFFFF' : '#333333',
                fontSize: 18 * fontSizeMultiplier
              }
            ]}>
              Upcoming Reviews
            </Text>

            {upcomingReviews.slice(0, 5).map((item, index) => renderUpcomingReview(item, index))}
          </View>
        )}
      </ScrollView>

      {/* Banner Ad */}
      <View style={styles.adContainer}>
        <BannerAdComponent
          placement={AD_PLACEMENTS.SETTINGS_BANNER}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  darkContainer: {
    backgroundColor: '#121212',
  },
  lightContainer: {
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    lineHeight: 22,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  statCard: {
    flex: 1,
    padding: 12,
    marginHorizontal: 2,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  todaySection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: 'bold',
    flex: 1,
  },
  startButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  startButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 4,
  },
  reviewItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  subjectBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subjectText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  intervalText: {
    fontStyle: 'italic',
  },
  questionText: {
    fontWeight: '500',
    marginBottom: 8,
    lineHeight: 20,
  },
  reviewStats: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    paddingTop: 8,
  },
  statsText: {
    fontStyle: 'italic',
  },
  upcomingSection: {
    paddingHorizontal: 20,
  },
  upcomingItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  upcomingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    fontStyle: 'italic',
  },
  upcomingQuestion: {
    fontWeight: '500',
    lineHeight: 18,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    marginTop: 16,
    textAlign: 'center',
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default SpacedRepetitionScreen;
