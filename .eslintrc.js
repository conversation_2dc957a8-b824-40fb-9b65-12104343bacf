module.exports = {
  extends: 'expo', // Use the Expo recommended ESLint configuration
  env: {
    'react-native/react-native': true, // Define react-native globals
  },
  plugins: [
    'react',
    'react-hooks',
    'react-native',
  ],
  rules: {
    // You can add or override specific rules here if needed
    // Example: 'react/prop-types': 'off', // Disable prop-types rule if using TypeScript or prefer not to use it
    'react-hooks/rules-of-hooks': 'error', // Checks rules of Hooks
    'react-hooks/exhaustive-deps': 'warn', // Checks effect dependencies
    'react-native/no-unused-styles': 'warn', // Warn about unused styles
    'react-native/split-platform-components': 'warn', // Warn if platform-specific components can be split
    'react-native/no-inline-styles': 'warn', // Warn about inline styles
    'react-native/no-color-literals': 'warn', // Warn about using color literals instead of variables/constants
    'react-native/no-raw-text': 'off', // Allows raw text outside of <Text> components (adjust if needed)
  },
};
