// File: functions/index.js

const functions = require("firebase-functions");
const axios = require("axios");
const cors = require("cors")({origin: true});

// NEW: Import Translate library and logger
const {Translate} = require("@google-cloud/translate").v2;
const logger = require("firebase-functions/logger"); // Use Firebase logger

// NEW: Initialize Translate client
// Assumes Application Default Credentials (ADC) or service account setup
// in Firebase environment
const translate = new Translate();

// Existing Groq function details (API key is still here, but not directly
// used by the new function)
const GROQ_API_KEY =
  "********************************************************";
// URL of the existing deployed function
const GROQ_FUNCTION_URL =
  "https://chatwithgroq-2asc2ekt6a-uc.a.run.app";

// Existing chatWithGroq function (keep as is)
exports.chatWithGroq = functions.https.onRequest(
    {invoker: "public"}, // Make function publicly callable
    (req, res) => {
      // Enable CORS
      cors(req, res, async () => {
        if (req.method !== "POST") {
          return res.status(405).send("Method Not Allowed");
        }

        const {messages} = req.body;
        if (!messages) {
          return res.status(400).send("Missing 'messages' field");
        }

        try {
          const response = await axios.post(
              "https://api.groq.com/openai/v1/chat/completions",
              {
                model: "llama3-8b-8192", // Updated to a supported Groq model
                messages: messages,
              },
              {
                headers: {
                  "Authorization": `Bearer ${GROQ_API_KEY}`,
                  "Content-Type": "application/json",
                },
              },
          );

          const reply = response.data.choices[0].message;
          res.status(200).json({reply});
        } catch (error) {
          logger.error( // Use Firebase logger
              "Groq API error:",
              error.response?.data || error.message,
          );
          res.status(500).send("Error calling Groq API");
        }
      }); // End CORS handler
    }, // End onRequest handler
); // End function definition


// --- NEW FUNCTION ---
// Function to handle chat request, call Groq, and translate the response
exports.chatWithTranslation = functions.https.onRequest(
    {
      invoker: "public", // Make function publicly callable
      timeoutSeconds: 120, // Increase timeout for potential translation delay
      memory: "256MB", // Specify memory if needed <-- Added comma
    },
    (req, res) => {
      cors(req, res, async () => { // Ensure CORS is handled
        if (req.method !== "POST") {
          return res.status(405).send("Method Not Allowed");
        }

        // Get messages and target language from request body
        const {messages, targetLanguage} = req.body;

        // Validate input
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
          logger.error(
              "Validation Error: Missing or invalid 'messages' field",
          );
          return res.status(400).send("Missing or invalid 'messages' field");
        }
        if (!targetLanguage || typeof targetLanguage !== "string") {
          logger.error(
              "Validation Error: Missing or invalid 'targetLanguage' field",
          );
          return res.status(400).send(
              "Missing or invalid 'targetLanguage' field",
          );
        }

        let originalText = "";
        let translatedText = null; // Default to null

        try {
          // 1. Call the existing Groq function URL
          logger.info(
              `Calling Groq function at ${GROQ_FUNCTION_URL} ` +
              `for target language ${targetLanguage}`,
          );
          const groqResponse = await axios.post(
              GROQ_FUNCTION_URL,
              {messages: messages}, // Pass messages through
              {
                headers: {"Content-Type": "application/json"},
                timeout: 60000, // Set timeout for the Groq call
              },
          );

          // Check if the response structure is as expected
          if (
            groqResponse.data &&
            groqResponse.data.reply &&
            groqResponse.data.reply.content
          ) {
            originalText = groqResponse.data.reply.content.trim();
            logger.info(
                `Received Groq response (length: ${originalText.length})`,
            );

            // 2. Translate the response if original text is not empty
            // and target is not English
            if (originalText && targetLanguage !== "en") {
              try {
                logger.info(`Attempting translation to ${targetLanguage}...`);
                const [translation] = await translate.translate(
                    originalText,
                    targetLanguage,
                );
                translatedText = translation;
                logger.info(
                    `Translation successful (length: ${
                      translatedText?.length
                    })`,
                );
              } catch (translateError) {
                // Log translation error but don't fail the entire request
                logger.error(
                    "Translation API error:",
                    translateError.message || translateError,
                );
                // Keep translatedText as null
              }
            } else if (targetLanguage === "en") {
              // If target is English, the original is the "translation"
              translatedText = originalText;
              logger.info("Target language is English, skipping translation.");
            } else {
              logger.info(
                  "Original text from Groq was empty, skipping translation.",
              );
            }
          } else {
            // Log unexpected structure from Groq function
            logger.error(
                "Invalid response structure received from Groq function:",
                groqResponse.data,
            );
            // Provide a user-friendly error
            originalText = "Error: Could not get a valid response " +
              "from the AI assistant.";
          }

          // 3. Send response back to the app
          logger.info("Sending response to client.");
          res.status(200).json({
            originalText: originalText,
            translatedText: translatedText, // Will be null if translation failed or wasn't needed
          });

        } catch (error) {
          // Log errors from calling Groq or other unexpected issues
          logger.error(
              "Error in chatWithTranslation function:",
              error.response?.data || error.message || error,
          );
          // Send back a generic error response
          // Include originalText if we got it before the error occurred
          const errorMsg = "Error processing request."; // Removed blank line and comment
          res.status(500).json({
            originalText: originalText || errorMsg,
            translatedText: null, // Ensure null on error
            error: "Failed to process chat request.",
          });
        }
      }); // End CORS handler
    }, // End onRequest handler
); // End function definition
