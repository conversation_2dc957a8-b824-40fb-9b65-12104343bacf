import React, { useEffect, useRef } from 'react';
import AdManager from '../src/services/AdManager';
import { AD_PLACEMENTS } from '../src/config/adConfig';

// Try to import native ads, fall back gracefully if not available
let InterstitialAd, AdEventType;
try {
  const GoogleMobileAds = require('react-native-google-mobile-ads');
  InterstitialAd = GoogleMobileAds.InterstitialAd;
  AdEventType = GoogleMobileAds.AdEventType;
} catch (_error) {
  console.log('Native Google Mobile Ads not available, interstitial ads disabled');
  InterstitialAd = null;
  AdEventType = null;
}

const InterstitialAdComponent = ({
  placement = AD_PLACEMENTS.QUIZ_INTERSTITIAL,
  onAdDismissed = () => {},
  onAdShown = () => {},
  autoShow = false
}) => {
  const interstitialRef = useRef(null);
  const isLoadedRef = useRef(false);
  const isShowingRef = useRef(false);

  const unitId = AdManager.getAdUnitForPlacement(placement);
  const requestConfig = AdManager.getAdRequestConfig(placement);

  useEffect(() => {
    // If native ads are not available, skip interstitial setup
    if (!InterstitialAd || !AdEventType) {
      console.log('Interstitial ads not available, skipping setup');
      onAdDismissed(); // Call callback to continue normal flow
      return;
    }

    // Create and load the interstitial ad
    const createInterstitial = () => {
      try {
        const interstitial = InterstitialAd.createForAdRequest(unitId, requestConfig);
        interstitialRef.current = interstitial;

        // Set up event listeners
        const unsubscribeLoaded = interstitial.addAdEventListener(
          AdEventType.LOADED,
          () => {
            console.log(`Interstitial ad loaded for ${placement}`);
            isLoadedRef.current = true;
            AdManager.resetErrors(placement, 'interstitial');

            // Auto-show if enabled and should show
            if (autoShow && AdManager.shouldShowInterstitial(placement)) {
              showAd();
            }
          }
        );

        const unsubscribeOpened = interstitial.addAdEventListener(
          AdEventType.OPENED,
          () => {
            console.log(`Interstitial ad opened for ${placement}`);
            isShowingRef.current = true;
            AdManager.recordImpression(placement, 'interstitial');
            onAdShown();
          }
        );

        const unsubscribeClosed = interstitial.addAdEventListener(
          AdEventType.CLOSED,
          () => {
            console.log(`Interstitial ad closed for ${placement}`);
            isShowingRef.current = false;
            isLoadedRef.current = false;
            onAdDismissed();

            // Clean up listeners
            unsubscribeLoaded();
            unsubscribeOpened();
            unsubscribeClosed();
            unsubscribeError();

            // Preload next ad
            setTimeout(createInterstitial, 1000);
          }
        );

        const unsubscribeError = interstitial.addAdEventListener(
          AdEventType.ERROR,
          (error) => {
            console.error(`Interstitial ad error for ${placement}:`, error);
            AdManager.recordError(placement, 'interstitial', error);
            isLoadedRef.current = false;
            isShowingRef.current = false;
            onAdDismissed();

            // Clean up listeners
            unsubscribeLoaded();
            unsubscribeOpened();
            unsubscribeClosed();
            unsubscribeError();

            // Retry after delay if not too many errors
            if (!AdManager.hasExcessiveErrors(placement, 'interstitial')) {
              setTimeout(createInterstitial, 5000);
            }
          }
        );

        // Load the ad
        interstitial.load();

      } catch (error) {
        console.error(`Failed to create interstitial ad for ${placement}:`, error);
        AdManager.recordError(placement, 'interstitial', error);
        onAdDismissed();
      }
    };

    createInterstitial();

    // Cleanup on unmount
    return () => {
      if (interstitialRef.current) {
        // Note: react-native-google-mobile-ads doesn't have a destroy method
        // The ad will be cleaned up automatically
        interstitialRef.current = null;
      }
    };
  }, [placement, unitId]);

  const showAd = () => {
    // If native ads are not available, skip showing ad
    if (!InterstitialAd) {
      console.log('Interstitial ads not available, skipping');
      onAdDismissed();
      return;
    }

    if (isLoadedRef.current && !isShowingRef.current && interstitialRef.current) {
      try {
        interstitialRef.current.show();
      } catch (error) {
        console.error(`Failed to show interstitial ad for ${placement}:`, error);
        AdManager.recordError(placement, 'interstitial', error);
        onAdDismissed();
      }
    } else {
      console.log(`Interstitial ad not ready for ${placement}`);
      onAdDismissed();
    }
  };

  const isLoaded = () => {
    return isLoadedRef.current;
  };

  const isShowing = () => {
    return isShowingRef.current;
  };

  // This component doesn't render anything
  return null;
};

export default InterstitialAdComponent;
