import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

const BookUnitsScreen = ({ route, navigation }) => {
  // You can access parameters passed during navigation like this:
  // const { bookId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Book Units Screen</Text>
      {/* Add your UI components for displaying book units here */}
      {/* Example: <Text>Book ID: {bookId}</Text> */}

      {/* Download Button */}
      <TouchableOpacity style={styles.downloadButton} onPress={() => console.log('Download pressed')}>
        <Text style={styles.downloadButtonText}>Download</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  downloadButton: {
    backgroundColor: '#007AFF', // iOS blue color
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25, // Makes it pill-shaped
    marginTop: 30, // Add some space above the button
    width: '90%', // Make button wider
    alignItems: 'center', // Center text horizontally
  },
  downloadButtonText: {
    color: '#FFFFFF', // White text
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Add more styles as needed
});

export default BookUnitsScreen;
