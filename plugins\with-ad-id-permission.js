const { withAndroidManifest } = require('@expo/config-plugins');

module.exports = function withAdIdPermission(config) {
  return withAndroidManifest(config, (config) => {
    const manifest = config.modResults;

    const permission = 'com.google.android.gms.permission.AD_ID';
    const alreadyAdded = manifest.manifest['uses-permission']?.some(
      (item) => item.$['android:name'] === permission
    );

    if (!alreadyAdded) {
      manifest.manifest['uses-permission'] = [
        ...(manifest.manifest['uses-permission'] || []),
        {
          $: {
            'android:name': permission,
          },
        },
      ];
    }

    return config;
  });
};
