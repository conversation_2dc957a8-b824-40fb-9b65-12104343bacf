import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { StyleSheet, Dimensions, View, ActivityIndicator, Text, TouchableOpacity, Alert, Linking, Platform } from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

// Try to import react-native-pdf with fallback
let Pdf = null;
try {
  Pdf = require('react-native-pdf').default;
  console.log('react-native-pdf loaded successfully');
} catch (_error) {
  console.log('react-native-pdf not available, using WebView fallback');
}

const PdfViewerScreen = () => {
    const route = useRoute();
    const navigation = useNavigation();
    // Always use WebView for Expo Go compatibility
    const [useWebView, setUseWebView] = useState(true);
    const [loading, setLoading] = useState(true);
    const [currentViewerIndex, setCurrentViewerIndex] = useState(0);
    const [retryCount, setRetryCount] = useState(0);
    const [error, setError] = useState(null);

    // Expecting the PDF source URI to be passed as a route parameter
    const { sourceUri } = route.params || {};

    // Check if this is a local file or remote URL
    const isLocalFile = sourceUri && (sourceUri.startsWith('file://') || sourceUri.includes('DocumentDirectory') || sourceUri.includes('cacheDirectory'));

    // Try multiple PDF viewing approaches for better compatibility
    const viewerOptions = useMemo(() => {
        if (isLocalFile) {
            // For local files, only return the direct URI
            return [sourceUri];
        } else {
            // For remote files, try multiple viewers with better encoding
            const encodedUri = encodeURIComponent(sourceUri);
            return [
                sourceUri, // Direct PDF URL first (works best for most PDFs)
                `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodedUri}`, // Mozilla PDF.js viewer
                `https://docs.google.com/gview?embedded=true&url=${encodedUri}`, // Google Docs viewer
                `https://drive.google.com/viewerng/viewer?embedded=true&url=${encodedUri}` // Google Drive viewer
            ];
        }
    }, [isLocalFile, sourceUri]);

    const handleWebViewError = useCallback((syntheticEvent) => {
        const { nativeEvent } = syntheticEvent;
        console.error('WebView PDF Error:', nativeEvent);
        console.error('Current PDF URL:', sourceUri);
        console.error('Current viewer option:', viewerOptions[currentViewerIndex]);

        // Try next viewer option if available
        if (currentViewerIndex < viewerOptions.length - 1) {
            console.log(`Trying alternative PDF viewer (${currentViewerIndex + 2}/${viewerOptions.length})`);
            setCurrentViewerIndex(currentViewerIndex + 1);
            setRetryCount(retryCount + 1);
            setLoading(true);
        } else {
            // All viewer options exhausted, show options
            setLoading(false);
            setError('Failed to load PDF in app');
            Alert.alert(
                'PDF Loading Error',
                'Unable to load PDF inside the app. Choose an option:',
                [
                    { text: 'Go Back', onPress: () => navigation.goBack() },
                    { text: 'Open Externally', onPress: () => openPdfExternally() },
                    { text: 'Retry', onPress: () => {
                        setCurrentViewerIndex(0);
                        setRetryCount(0);
                        setLoading(true);
                        setError(null);
                    }}
                ]
            );
        }
    }, [currentViewerIndex, viewerOptions, retryCount, sourceUri, navigation]);

    const copyToShareableLocation = async (sourceUri) => {
        try {
            // Create a shareable copy in the cache directory
            const fileName = sourceUri.split('/').pop() || 'document.pdf';
            const shareableUri = `${FileSystem.cacheDirectory}shared_${Date.now()}_${fileName}`;

            console.log('Copying file from', sourceUri, 'to', shareableUri);
            await FileSystem.copyAsync({
                from: sourceUri,
                to: shareableUri
            });

            return shareableUri;
        } catch (error) {
            console.error('Error copying file to shareable location:', error);
            throw error;
        }
    };

    const openPdfExternally = async () => {
        try {
            console.log('Opening PDF externally:', sourceUri);

            // For local files, copy to shareable location first
            if (isLocalFile) {
                console.log('Handling local PDF file');

                try {
                    // Try to copy to a shareable location
                    const shareableUri = await copyToShareableLocation(sourceUri);
                    console.log('File copied to shareable location:', shareableUri);

                    if (await Sharing.isAvailableAsync()) {
                        await Sharing.shareAsync(shareableUri, {
                            mimeType: 'application/pdf',
                            dialogTitle: 'Open PDF with...',
                            UTI: 'com.adobe.pdf'
                        });
                    } else {
                        Alert.alert(
                            'Sharing Not Available',
                            'File sharing is not available on this device.'
                        );
                    }
                } catch (copyError) {
                    console.error('Failed to copy file, trying direct sharing:', copyError);
                    // Fallback: try direct sharing (might fail on newer Android)
                    if (await Sharing.isAvailableAsync()) {
                        await Sharing.shareAsync(sourceUri, {
                            mimeType: 'application/pdf',
                            dialogTitle: 'Open PDF with...'
                        });
                    } else {
                        Alert.alert(
                            'Cannot Share File',
                            'Unable to share this PDF file. The file may be in a protected location.'
                        );
                    }
                }
            } else {
                // For remote files, try to open with browser/external app
                console.log('Opening remote PDF file');
                if (Platform.OS === 'android') {
                    const canOpen = await Linking.canOpenURL(sourceUri);
                    if (canOpen) {
                        await Linking.openURL(sourceUri);
                    } else {
                        // Fallback: try to share the URL
                        if (await Sharing.isAvailableAsync()) {
                            await Sharing.shareAsync(sourceUri);
                        } else {
                            Alert.alert(
                                'Cannot Open PDF',
                                'Please install a PDF viewer app from the Play Store.'
                            );
                        }
                    }
                } else {
                    // For iOS
                    await Linking.openURL(sourceUri);
                }
            }
        } catch (error) {
            console.error('Error opening PDF externally:', error);
            Alert.alert(
                'Error',
                'Could not open PDF. Please make sure you have a PDF viewer app installed.'
            );
        }
    };



    // Add timeout for loading and URL validation
    useEffect(() => {
        console.log('PDF Viewer initialized with sourceUri:', sourceUri);
        console.log('Is local file:', isLocalFile);
        console.log('Native PDF available:', !!Pdf);

        // Reset states when viewer option changes
        setLoading(true);
        setError(null);

        // For local files, offer to share since WebView cannot display them
        if (isLocalFile) {
            console.log('Local PDF file detected - offering to share externally');
            setLoading(false);
            Alert.alert(
                'Local PDF File',
                'This PDF file is stored locally and cannot be displayed in the app. Would you like to open it with another app?',
                [
                    { text: 'Go Back', onPress: () => navigation.goBack() },
                    { text: 'Open Externally', onPress: () => openPdfExternally() }
                ]
            );
            return;
        } else {
            // For remote files, validate URL accessibility first
            const validateUrl = async () => {
                try {
                    console.log('Validating PDF URL:', sourceUri);

                    // Simple fetch without AbortController for better compatibility
                    const response = await Promise.race([
                        fetch(sourceUri, { method: 'HEAD' }),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Timeout')), 10000)
                        )
                    ]);

                    console.log('URL validation response:', response.status, response.statusText);
                    if (!response.ok) {
                        console.error('PDF URL not accessible:', response.status, response.statusText);
                        // Don't immediately fail - let the viewer try anyway
                        console.log('Proceeding with PDF loading despite validation failure');
                    }
                } catch (error) {
                    console.error('URL validation failed:', error);
                    // Don't immediately fail - let the viewer try anyway
                    console.log('Proceeding with PDF loading despite validation failure');
                }
            };

            validateUrl();
        }

        // Set a timeout to prevent infinite loading
        const loadingTimeout = setTimeout(() => {
            if (loading) {
                console.log('PDF loading timeout reached');
                handleWebViewError({ nativeEvent: { description: 'Loading timeout (20s)' } });
            }
        }, 20000); // 20 second timeout

        return () => clearTimeout(loadingTimeout);
    }, [currentViewerIndex, sourceUri, isLocalFile]);

    if (!sourceUri) {
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                        <Ionicons name="arrow-back" size={24} color="#333" />
                    </TouchableOpacity>
                    <Text style={styles.title}>PDF Viewer</Text>
                </View>
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Error: No PDF source provided.</Text>
                </View>
            </View>
        );
    }

    // Always try to display PDF inside the app first
    // Only show external app option as a last resort if both native PDF and WebView fail

    const handleNativePdfError = (error) => {
        console.error('Native PDF Error:', error);

        // For local files, don't switch to WebView as it cannot handle local files
        if (isLocalFile) {
            console.error('Native PDF failed for local file - cannot use WebView fallback');
            setLoading(false);
            setError('Failed to load local PDF file');
            Alert.alert(
                'PDF Loading Error',
                'Failed to load the local PDF file. The file may be corrupted or in an unsupported format.',
                [
                    { text: 'Go Back', onPress: () => navigation.goBack() },
                    { text: 'Share File', onPress: () => openPdfExternally() }
                ]
            );
        } else {
            // For remote files, switch to WebView fallback
            console.log('Switching to WebView fallback for remote PDF');
            setUseWebView(true);
            setLoading(true); // Restart loading with WebView
        }
    };

    const renderNativePdf = () => (
        <Pdf
            trustAllCerts={false}
            source={{ uri: sourceUri, cache: true }}
            onLoadComplete={(numberOfPages) => {
                console.log(`PDF loaded: ${numberOfPages} pages`);
                setLoading(false);
                setError(null);
            }}
            onPageChanged={(page, numberOfPages) => {
                console.log(`Current page: ${page} of ${numberOfPages}`);
            }}
            onError={handleNativePdfError}
            onPressLink={(uri) => {
                console.log(`Link pressed: ${uri}`);
                Linking.openURL(uri);
            }}
            style={styles.pdf}
            renderActivityIndicator={() => (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator color="blue" size="large" />
                    <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
            )}
            enablePaging={true}
            enableRTL={false}
            enableDoubleTapZoom={true}
            enableAnnotationRendering={true}
            password=""
            spacing={10}
            horizontal={false}
        />
    );



    const renderWebViewPdf = () => {
        console.log(`Rendering WebView with viewer option ${currentViewerIndex}: ${viewerOptions[currentViewerIndex]}`);

        return (
            <WebView
                source={{ uri: viewerOptions[currentViewerIndex] }}
                style={styles.pdf}
                onLoadStart={() => {
                    console.log('WebView load started');
                    setLoading(true);
                }}
                onLoadEnd={() => {
                    console.log('WebView load ended');
                    setLoading(false);
                }}
                onError={handleWebViewError}
                startInLoadingState={true}
                renderLoading={() => (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator color="blue" size="large" />
                        <Text style={styles.loadingText}>Loading PDF...</Text>
                        {retryCount > 0 && (
                            <Text style={styles.retryText}>
                                Trying alternative viewer ({currentViewerIndex + 1}/{viewerOptions.length})
                            </Text>
                        )}
                    </View>
                )}
                // Additional props for better PDF handling
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                allowFileAccess={true}
                allowUniversalAccessFromFileURLs={true}
                mixedContentMode="compatibility"
                onHttpError={(syntheticEvent) => {
                    const { nativeEvent } = syntheticEvent;
                    console.error('HTTP Error:', nativeEvent);
                    handleWebViewError(syntheticEvent);
                }}
            />
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color="#333" />
                </TouchableOpacity>
                <Text style={styles.title}>PDF Viewer</Text>
                {Pdf && (
                    <TouchableOpacity
                        onPress={() => setUseWebView(!useWebView)}
                        style={styles.toggleButton}
                    >
                        <Ionicons
                            name={useWebView ? "document" : "globe"}
                            size={20}
                            color="#007AFF"
                        />
                    </TouchableOpacity>
                )}
            </View>

            {loading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator color="blue" size="large" />
                    <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
            )}

            {useWebView ? renderWebViewPdf() : renderNativePdf()}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#e9ecef',
    },
    backButton: {
        padding: 8,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    toggleButton: {
        padding: 8,
    },
    pdf: {
        flex: 1,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        color: 'red',
        textAlign: 'center',
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 1000,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#333',
    },
    retryText: {
        marginTop: 5,
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    errorSubText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
        marginTop: 10,
        paddingHorizontal: 20,
        lineHeight: 20,
    },
    retryButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 8,
        marginTop: 20,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default PdfViewerScreen;
