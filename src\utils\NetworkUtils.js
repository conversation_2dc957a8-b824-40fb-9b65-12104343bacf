// Network utility functions for ad optimization
import { Platform } from 'react-native';

// Network connection types
export const NETWORK_TYPES = {
  WIFI: 'wifi',
  CELLULAR_4G: '4g',
  CELLULAR_3G: '3g',
  CELLULAR_2G: '2g',
  UNKNOWN: 'unknown',
  NONE: 'none'
};

// Network quality levels
export const NETWORK_QUALITY = {
  EXCELLENT: 'excellent', // WiFi or 4G+
  GOOD: 'good',          // 4G
  FAIR: 'fair',          // 3G
  POOR: 'poor',          // 2G or slower
  OFFLINE: 'offline'     // No connection
};

class NetworkUtils {
  constructor() {
    this.currentNetworkType = NETWORK_TYPES.UNKNOWN;
    this.currentNetworkQuality = NETWORK_QUALITY.GOOD;
    this.isConnected = true;
    this.lastSpeedTest = null;
    this.speedTestResults = [];
  }

  // Get network-optimized ad configuration
  getAdConfigForNetwork() {
    const quality = this.getCurrentNetworkQuality();
    
    switch (quality) {
      case NETWORK_QUALITY.EXCELLENT:
        return {
          timeout: 8000,
          retries: 8,
          retryDelay: 2000,
          enablePreloading: true,
          enableRefresh: true,
          refreshInterval: 45000
        };
      
      case NETWORK_QUALITY.GOOD:
        return {
          timeout: 12000,
          retries: 6,
          retryDelay: 3000,
          enablePreloading: true,
          enableRefresh: true,
          refreshInterval: 60000
        };
      
      case NETWORK_QUALITY.FAIR:
        return {
          timeout: 18000,
          retries: 4,
          retryDelay: 5000,
          enablePreloading: false,
          enableRefresh: false,
          refreshInterval: 90000
        };
      
      case NETWORK_QUALITY.POOR:
        return {
          timeout: 25000,
          retries: 2,
          retryDelay: 8000,
          enablePreloading: false,
          enableRefresh: false,
          refreshInterval: 120000
        };
      
      default:
        return {
          timeout: 15000,
          retries: 5,
          retryDelay: 4000,
          enablePreloading: false,
          enableRefresh: true,
          refreshInterval: 60000
        };
    }
  }

  // Detect network quality based on connection type
  getCurrentNetworkQuality() {
    if (!this.isConnected) {
      return NETWORK_QUALITY.OFFLINE;
    }

    switch (this.currentNetworkType) {
      case NETWORK_TYPES.WIFI:
        return NETWORK_QUALITY.EXCELLENT;
      case NETWORK_TYPES.CELLULAR_4G:
        return NETWORK_QUALITY.GOOD;
      case NETWORK_TYPES.CELLULAR_3G:
        return NETWORK_QUALITY.FAIR;
      case NETWORK_TYPES.CELLULAR_2G:
        return NETWORK_QUALITY.POOR;
      default:
        return NETWORK_QUALITY.GOOD; // Default assumption
    }
  }

  // Simple speed test for network quality assessment
  async performSpeedTest() {
    const testStartTime = Date.now();
    const testUrl = 'https://www.google.com/favicon.ico'; // Small file for testing
    
    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        cache: 'no-cache'
      });
      
      if (response.ok) {
        const testEndTime = Date.now();
        const duration = testEndTime - testStartTime;
        
        // Store speed test result
        this.lastSpeedTest = {
          timestamp: testEndTime,
          duration,
          success: true
        };
        
        this.speedTestResults.push(this.lastSpeedTest);
        
        // Keep only last 5 results
        if (this.speedTestResults.length > 5) {
          this.speedTestResults.shift();
        }
        
        // Update network quality based on speed
        this.updateNetworkQualityFromSpeed(duration);
        
        console.log(`Network speed test completed in ${duration}ms`);
        return duration;
      }
    } catch (error) {
      console.warn('Network speed test failed:', error);
      this.lastSpeedTest = {
        timestamp: Date.now(),
        duration: null,
        success: false,
        error: error.message
      };
      return null;
    }
  }

  // Update network quality based on speed test results
  updateNetworkQualityFromSpeed(duration) {
    if (duration < 500) {
      this.currentNetworkQuality = NETWORK_QUALITY.EXCELLENT;
    } else if (duration < 1500) {
      this.currentNetworkQuality = NETWORK_QUALITY.GOOD;
    } else if (duration < 3000) {
      this.currentNetworkQuality = NETWORK_QUALITY.FAIR;
    } else {
      this.currentNetworkQuality = NETWORK_QUALITY.POOR;
    }
  }

  // Get average speed from recent tests
  getAverageSpeed() {
    const successfulTests = this.speedTestResults.filter(test => test.success);
    if (successfulTests.length === 0) return null;
    
    const totalDuration = successfulTests.reduce((sum, test) => sum + test.duration, 0);
    return totalDuration / successfulTests.length;
  }

  // Check if network is suitable for ads
  isNetworkSuitableForAds() {
    const quality = this.getCurrentNetworkQuality();
    return quality !== NETWORK_QUALITY.OFFLINE && quality !== NETWORK_QUALITY.POOR;
  }

  // Get recommended ad size based on network
  getRecommendedAdSize() {
    const quality = this.getCurrentNetworkQuality();
    
    switch (quality) {
      case NETWORK_QUALITY.EXCELLENT:
      case NETWORK_QUALITY.GOOD:
        return 'LARGE_BANNER'; // 320x250
      case NETWORK_QUALITY.FAIR:
        return 'MEDIUM_BANNER'; // 320x100
      case NETWORK_QUALITY.POOR:
        return 'SMALL_BANNER'; // 320x50
      default:
        return 'MEDIUM_BANNER';
    }
  }

  // Update connection status
  updateConnectionStatus(isConnected, networkType = NETWORK_TYPES.UNKNOWN) {
    this.isConnected = isConnected;
    this.currentNetworkType = networkType;
    
    if (!isConnected) {
      this.currentNetworkQuality = NETWORK_QUALITY.OFFLINE;
    } else {
      this.currentNetworkQuality = this.getCurrentNetworkQuality();
    }
    
    console.log(`Network status updated: ${isConnected ? 'Connected' : 'Disconnected'} (${networkType})`);
  }

  // Get network info for debugging
  getNetworkInfo() {
    return {
      isConnected: this.isConnected,
      networkType: this.currentNetworkType,
      networkQuality: this.currentNetworkQuality,
      lastSpeedTest: this.lastSpeedTest,
      averageSpeed: this.getAverageSpeed(),
      adConfig: this.getAdConfigForNetwork()
    };
  }
}

// Export singleton instance
export default new NetworkUtils();
