# APK Diagnostic Guide

## APK Build Status ✅
Your APK has been successfully built at:
`android/app/build/outputs/apk/release/app-release.apk`

**APK Details:**
- Application ID: `com.gokul719.snack97152fc1f368437dac54171df4ba22bd`
- Version Code: 1
- Version Name: 1.2.0
- Min SDK: 24 (Android 7.0)
- Target SDK: Latest

## Common APK Issues & Solutions

### 1. **Installation Issues**

**Problem:** APK won't install
**Solutions:**
```bash
# Enable Unknown Sources on your device:
# Settings > Security > Unknown Sources (Enable)

# Or install via ADB:
adb install android/app/build/outputs/apk/release/app-release.apk

# Force reinstall if already installed:
adb install -r android/app/build/outputs/apk/release/app-release.apk
```

### 2. **App Crashes on Startup**

**Problem:** App opens then immediately crashes
**Potential Causes:**
- Firebase configuration issues
- AdMob configuration problems
- Missing permissions
- Voice recognition module conflicts

**Debug Steps:**
```bash
# Check crash logs:
adb logcat | grep -E "(AndroidRuntime|FATAL|ERROR)"

# Check specific app logs:
adb logcat | grep "com.gokul719.snack97152fc1f368437dac54171df4ba22bd"
```

### 3. **Firebase Connection Issues**

**Problem:** App can't connect to Firebase
**Check:**
- Internet connection on device
- Firebase project configuration
- Google Services JSON file

### 4. **AdMob Issues**

**Problem:** Ads not loading or causing crashes
**Note:** I noticed duplicate AdMob APPLICATION_ID entries in AndroidManifest.xml
**Fix Applied:** The app should handle AdMob gracefully

### 5. **Voice Recognition Issues**

**Problem:** Voice features not working
**Expected:** Voice recognition should work in APK (unlike Expo Go)
**Check:** Microphone permissions are granted

## Testing Checklist

### Basic Functionality:
- [ ] App installs successfully
- [ ] App opens without crashing
- [ ] Login/Register works
- [ ] Navigation between screens works
- [ ] Firebase authentication works

### Advanced Features:
- [ ] Voice recognition works (microphone permission)
- [ ] PDF viewing works
- [ ] AI chat functionality works
- [ ] Ads display properly
- [ ] Settings save correctly

### Permissions Check:
- [ ] Camera permission (if using camera features)
- [ ] Microphone permission (for voice recognition)
- [ ] Internet permission (for Firebase/API calls)
- [ ] Storage permission (for file operations)

## Debugging Commands

### Install APK:
```bash
adb install android/app/build/outputs/apk/release/app-release.apk
```

### View Real-time Logs:
```bash
adb logcat | grep -E "(ReactNativeJS|ExpoModules|Firebase|AdMob)"
```

### Check App Info:
```bash
adb shell dumpsys package com.gokul719.snack97152fc1f368437dac54171df4ba22bd
```

### Clear App Data (if needed):
```bash
adb shell pm clear com.gokul719.snack97152fc1f368437dac54171df4ba22bd
```

## Next Steps

1. **Install the APK** on your device
2. **Test basic functionality** (login, navigation)
3. **Check logs** if there are issues
4. **Report specific errors** for targeted fixes

## Build Commands for Future Reference

### Local APK Build:
```bash
npx expo prebuild --platform android
cd android && ./gradlew assembleRelease
```

### EAS Build (Alternative):
```bash
eas build -p android --profile local-apk
```

The APK should work properly on real devices with all features including voice recognition!
