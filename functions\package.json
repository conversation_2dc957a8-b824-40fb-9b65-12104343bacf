{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"axios": "^1.7.2", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "follow-redirects": "^1.15.9", "form-data": "^4.0.2", "proxy-from-env": "^1.1.0", "@google-cloud/translate": "^8.2.0"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}