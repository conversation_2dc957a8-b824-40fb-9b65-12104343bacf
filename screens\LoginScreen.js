import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ImageBackground, Alert, Image } from 'react-native'; // Import Alert and Image
import { Ionicons } from '@expo/vector-icons';
import { auth } from '../firebaseConfig'; // Import Firebase auth (adjust path if needed)
import { signInWithEmailAndPassword } from 'firebase/auth'; // Import Firebase sign in function

const LoginScreen = ({ navigation }) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [email, setEmail] = useState(''); // Add state for email
  const [password, setPassword] = useState(''); // Add state for password

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleLogin = () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password.');
      return;
    }
    signInWithEmailAndPassword(auth, email, password)
      .then((userCredential) => {
        // Signed in
        // Login successful, onAuthStateChanged in App.js will handle navigation
        console.log('Logged in user:', userCredential.user.email); 
        // No explicit navigation needed here
      })
      .catch((error) => {
        console.error('Login Error:', error.code, error.message);
        let errorMessage = 'An unexpected error occurred. Please try again.';
        if (error.code === 'auth/invalid-credential' || error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.code === 'auth/invalid-email') {
          errorMessage = 'Please enter a valid email address.';
        }
        Alert.alert('Login Failed', errorMessage); // Show more specific error to user
      });
  };

  return (
    <ImageBackground
      source={require('../assets/images/background.jpg')} // Set background image source
      style={styles.container}
    >
      {/* Add overlay for readability */}
      <View style={styles.overlay}>
        <Image source={require('../assets/images/logo.png')} style={styles.logo} /> 
        <Text style={styles.title}>Login</Text>
        <TextInput
          style={styles.input}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
        value={email} // Bind value to state
        onChangeText={setEmail} // Update state on change
      />
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.inputField}
          placeholder="Password"
          secureTextEntry={!isPasswordVisible}
          value={password} // Bind value to state
          onChangeText={setPassword} // Update state on change
        />
        <TouchableOpacity onPress={togglePasswordVisibility} style={styles.iconStyle}>
          <Ionicons name={isPasswordVisible ? 'eye-off' : 'eye'} size={24} color="#aaa" />
        </TouchableOpacity>
      </View>
      <TouchableOpacity style={styles.button} onPress={handleLogin}> // Call handleLogin on press
        <Text style={styles.buttonText}>Login</Text>
      </TouchableOpacity>
      <TouchableOpacity>
        <Text 
          style={styles.forgotPassword}
          onPress={() => navigation.navigate('ForgotPassword')} // Add onPress handler
        >Forgot Password?</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.signUpButton}
        onPress={() => navigation.navigate('Register')} // Add onPress handler
      >
        <Text style={styles.signUpText}>Sign Up</Text>
        </TouchableOpacity>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%', // Ensure ImageBackground covers the screen width
    height: '100%', // Ensure ImageBackground covers the screen height
  },
  overlay: { // Style for the overlay View
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent black overlay
    padding: 20,
  },
  logo: { // Style for the logo
    width: 150, // Adjust width as needed
    height: 150, // Adjust height as needed
    resizeMode: 'contain', // Ensure the logo scales correctly
    marginBottom: 30, // Add some space below the logo
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#fff', // Change text color to white for better contrast
  },
  inputContainer: { // Style for the password input container
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    marginBottom: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 10,
  },
  inputField: { // Style for the TextInput inside the container
    flex: 1,
    height: '100%',
  },
  iconStyle: { // Style for the icon TouchableOpacity
    padding: 5,
  },
  input: { // Keep original input style for email
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Slightly transparent white background for inputs
  },
  button: {
    width: '100%',
    height: 50,
    backgroundColor: '#007BFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  forgotPassword: {
    color: '#ADD8E6', // Light blue color for better contrast
    fontSize: 14,
    marginTop: 10,
    textDecorationLine: 'underline',
    fontWeight: 'bold', // Make text bold
  },
  signUpButton: {
    marginTop: 20,
  },
  signUpText: {
    color: '#ADD8E6', // Light blue color for better contrast
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LoginScreen;
