rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read, create, and update their own score document
    match /userScores/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
     match /userScores {
      allow read: if true;
    }
    match /users/{userId} {
      allow read: if true;
    }
  }
}
